package cn.leo.helper

import kotlin.random.Random

class AIQuestionGenerator {
    
    // 编程相关问题模板
    private val programmingQuestions = listOf(
        "如何优化这段代码的性能？",
        "这个函数有什么潜在的bug吗？",
        "请解释一下这个算法的时间复杂度",
        "如何重构这段代码使其更易读？",
        "这段代码遵循了哪些设计模式？",
        "如何为这个方法编写单元测试？",
        "这个类的职责是否过于复杂？",
        "如何改进这个数据结构的设计？",
        "这段代码有内存泄漏的风险吗？",
        "如何使这个方法更加线程安全？"
    )
    
    // Kotlin特定问题
    private val kotlinQuestions = listOf(
        "这段Kotlin代码可以如何简化？",
        "如何使用Kotlin的扩展函数改进这段代码？",
        "这里应该使用data class还是普通class？",
        "如何利用Kotlin的null安全特性？",
        "这段代码可以用协程来优化吗？",
        "如何使用Kotlin的委托属性？",
        "这里适合使用sealed class吗？",
        "如何用Kotlin的高阶函数重写这段代码？",
        "这段代码的作用域函数使用是否合适？",
        "如何利用Kotlin的类型推断？"
    )
    
    // Java特定问题
    private val javaQuestions = listOf(
        "这段Java代码如何迁移到Java 17？",
        "如何使用Stream API优化这段代码？",
        "这里应该使用Optional吗？",
        "如何改进这个异常处理？",
        "这段代码的泛型使用是否正确？",
        "如何使用lambda表达式简化这段代码？",
        "这里需要考虑并发安全吗？",
        "如何优化这个集合操作？",
        "这段代码符合Java编码规范吗？",
        "如何使用注解改进这段代码？"
    )
    
    // 架构和设计问题
    private val architectureQuestions = listOf(
        "这个模块的架构设计合理吗？",
        "如何改进这个系统的可扩展性？",
        "这里应该使用什么设计模式？",
        "如何降低这些类之间的耦合度？",
        "这个接口设计是否符合SOLID原则？",
        "如何改进这个系统的容错性？",
        "这个数据流设计有什么问题？",
        "如何优化这个系统的性能瓶颈？",
        "这个模块的职责划分是否清晰？",
        "如何改进这个API的设计？"
    )
    
    // 调试和问题排查
    private val debuggingQuestions = listOf(
        "这个错误可能的原因是什么？",
        "如何调试这个性能问题？",
        "这个异常应该如何处理？",
        "如何定位这个内存泄漏？",
        "这个并发问题如何解决？",
        "如何分析这个崩溃日志？",
        "这个网络请求为什么失败？",
        "如何优化这个数据库查询？",
        "这个缓存策略有什么问题？",
        "如何监控这个系统的健康状态？"
    )
    
    // 代码审查问题
    private val codeReviewQuestions = listOf(
        "这段代码有什么可以改进的地方？",
        "代码的可读性如何提升？",
        "这里有潜在的安全风险吗？",
        "错误处理是否充分？",
        "代码注释是否清晰？",
        "变量命名是否合适？",
        "这个方法是否过于复杂？",
        "是否遵循了团队的编码规范？",
        "这段代码的测试覆盖率如何？",
        "是否有重复代码需要提取？"
    )
    
    // 学习和解释问题
    private val learningQuestions = listOf(
        "请解释这个概念的原理",
        "这个技术的优缺点是什么？",
        "如何学习这个新框架？",
        "这个工具的最佳实践是什么？",
        "这个算法的应用场景有哪些？",
        "如何选择合适的数据结构？",
        "这个设计模式什么时候使用？",
        "如何评估技术方案的可行性？",
        "这个库的核心特性是什么？",
        "如何跟上技术发展趋势？"
    )
    
    private val questionCategories = listOf(
        programmingQuestions,
        kotlinQuestions, 
        javaQuestions,
        architectureQuestions,
        debuggingQuestions,
        codeReviewQuestions,
        learningQuestions
    )
    
    fun generateQuestion(): String {
        // 随机选择一个问题类别
        val category = questionCategories.random()
        
        // 从该类别中随机选择一个问题
        val baseQuestion = category.random()
        
        // 有时候添加一些上下文或变化
        return when (Random.nextInt(10)) {
            0, 1 -> "请帮我分析：$baseQuestion"
            2, 3 -> "我想了解：$baseQuestion"
            4, 5 -> "能否解释：$baseQuestion"
            6 -> "关于当前代码，$baseQuestion"
            7 -> "在这个项目中，$baseQuestion"
            8 -> "从最佳实践角度，$baseQuestion"
            else -> baseQuestion
        }
    }
    
    fun generateQuestionWithContext(fileExtension: String? = null): String {
        val question = when (fileExtension?.lowercase()) {
            "kt" -> kotlinQuestions.random()
            "java" -> javaQuestions.random()
            "js", "ts" -> "如何用现代JavaScript/TypeScript改进这段代码？"
            "py" -> "这段Python代码如何优化？"
            "go" -> "这段Go代码遵循了最佳实践吗？"
            "rs" -> "如何让这段Rust代码更加安全和高效？"
            "cpp", "cc", "cxx" -> "这段C++代码有什么可以改进的地方？"
            "cs" -> "这段C#代码如何重构？"
            else -> generateQuestion()
        }
        
        return question
    }
    
    fun generateSpecificQuestion(type: QuestionType): String {
        return when (type) {
            QuestionType.PROGRAMMING -> programmingQuestions.random()
            QuestionType.KOTLIN -> kotlinQuestions.random()
            QuestionType.JAVA -> javaQuestions.random()
            QuestionType.ARCHITECTURE -> architectureQuestions.random()
            QuestionType.DEBUGGING -> debuggingQuestions.random()
            QuestionType.CODE_REVIEW -> codeReviewQuestions.random()
            QuestionType.LEARNING -> learningQuestions.random()
        }
    }
    
    fun generateMultipleQuestions(count: Int = 3): List<String> {
        val questions = mutableListOf<String>()
        val usedQuestions = mutableSetOf<String>()
        
        repeat(count) {
            var question: String
            do {
                question = generateQuestion()
            } while (question in usedQuestions && usedQuestions.size < getTotalQuestionCount())
            
            questions.add(question)
            usedQuestions.add(question)
        }
        
        return questions
    }
    
    private fun getTotalQuestionCount(): Int {
        return questionCategories.sumOf { it.size }
    }
    
    enum class QuestionType {
        PROGRAMMING,
        KOTLIN,
        JAVA,
        ARCHITECTURE,
        DEBUGGING,
        CODE_REVIEW,
        LEARNING
    }
}
