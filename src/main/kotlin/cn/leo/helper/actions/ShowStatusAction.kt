package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages
import cn.leo.helper.KpiHelperConfig
import cn.leo.helper.AIPluginDetector
import java.text.SimpleDateFormat
import java.util.*

class ShowStatusAction : AnAction() {
    
    override fun actionPerformed(e: AnActionEvent) {
        val config = KpiHelperConfig.getInstance()
        val detector = AIPluginDetector()
        
        // 检测AI插件
        val aiPlugins = detector.detectAIPlugins()
        
        // 格式化最后交互时间
        val lastInteractionTime = if (config.lastInteractionTime > 0) {
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.format(Date(config.lastInteractionTime))
        } else {
            "从未执行"
        }
        
        // 计算成功率
        val successRate = String.format("%.1f%%", config.getSuccessRate() * 100)
        
        // 构建状态信息
        val statusMessage = buildString {
            appendLine("=== AI助手自动化状态 ===")
            appendLine()
            appendLine("基本状态:")
            appendLine("• 功能状态: ${if (config.enabled) "已启用" else "已禁用"}")
            appendLine("• 工作时间限制: ${if (config.enableWorkingHoursOnly) "已启用 (${config.workingHoursStart}:00-${config.workingHoursEnd}:00)" else "已禁用"}")
            appendLine("• 仅工作日: ${if (config.enableWeekdaysOnly) "是" else "否"}")
            appendLine("• 当前是否在工作时间: ${if (config.isInWorkingHours()) "是" else "否"}")
            appendLine()
            
            appendLine("执行配置:")
            appendLine("• 执行间隔: ${config.minIntervalMinutes}-${config.maxIntervalMinutes} 分钟")
            appendLine("• 初始延迟: ${config.initialDelayMinutes} 分钟")
            appendLine("• 最大重试次数: ${config.maxRetries}")
            appendLine("• 模拟用户输入: ${if (config.simulateUserInput) "是" else "否"}")
            appendLine()
            
            appendLine("检测到的AI插件 (${aiPlugins.size}个):")
            if (aiPlugins.isEmpty()) {
                appendLine("• 未检测到任何AI插件")
            } else {
                aiPlugins.forEach { plugin ->
                    appendLine("• $plugin")
                }
            }
            appendLine()
            
            appendLine("统计信息:")
            appendLine("• 总交互次数: ${config.totalInteractions}")
            appendLine("• 成功次数: ${config.successfulInteractions}")
            appendLine("• 失败次数: ${config.failedInteractions}")
            appendLine("• 成功率: $successRate")
            appendLine("• 最后执行时间: $lastInteractionTime")
            appendLine()
            
            appendLine("问题配置:")
            appendLine("• 随机问题: ${if (config.enableRandomQuestions) "已启用" else "已禁用"}")
            appendLine("• 启用的问题类型: ${config.questionTypes.joinToString(", ")}")
            appendLine("• 自定义问题数量: ${config.customQuestions.size}")
            
            if (config.preferredAIPlugins.isNotEmpty()) {
                appendLine()
                appendLine("首选AI插件:")
                config.preferredAIPlugins.forEach { plugin ->
                    appendLine("• $plugin")
                }
            }
        }
        
        Messages.showInfoMessage(
            e.project,
            statusMessage,
            "KPI助手状态"
        )
    }
}
