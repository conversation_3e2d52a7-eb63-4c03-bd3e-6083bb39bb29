package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages
import cn.leo.helper.KpiHelperConfig

class ToggleKpiHelperAction : AnAction() {
    
    override fun actionPerformed(e: AnActionEvent) {
        val config = KpiHelperConfig.getInstance()
        
        config.enabled = !config.enabled
        
        val status = if (config.enabled) "已启用" else "已禁用"
        val message = "AI助手自动化功能$status"
        
        Messages.showInfoMessage(
            e.project,
            message,
            "KPI助手"
        )
    }
    
    override fun update(e: AnActionEvent) {
        val config = KpiHelperConfig.getInstance()
        val text = if (config.enabled) "禁用自动化" else "启用自动化"
        e.presentation.text = text
    }
}
