package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.ui.Messages
import cn.leo.helper.AIQuestionGenerator
import cn.leo.helper.KpiHelperConfig
import cn.leo.helper.YTOAIInteractor
import com.intellij.openapi.diagnostic.Logger

class TriggerNowAction : AnAction() {
    
    private val logger = Logger.getInstance(TriggerNowAction::class.java)
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            Messages.showErrorDialog("没有打开的项目", "错误")
            return
        }
        
        val config = KpiHelperConfig.getInstance()
        
        try {
            // 生成问题
            val generator = AIQuestionGenerator()
            val question = generator.generateQuestion()

            // 使用YTO AI交互器
            val interactor = YTOAIInteractor(project)

            // 更新统计
            config.incrementInteractionCount()

            // 执行AI交互
            val success = interactor.executeInteraction(question)

            if (success) {
                config.incrementSuccessCount()
                Messages.showInfoMessage(
                    project,
                    "已成功触发YTO AI交互！\n" +
                    "问题: $question",
                    "执行成功"
                )
            } else {
                config.incrementFailureCount()
                Messages.showWarningDialog(
                    project,
                    "YTO AI交互失败。\n" +
                    "问题: $question\n\n" +
                    "请检查：\n" +
                    "1. YTO AI插件是否已安装并启用\n" +
                    "2. 插件是否正常工作\n" +
                    "3. 查看日志获取详细错误信息",
                    "执行失败"
                )
            }

        } catch (e: Exception) {
            logger.error("手动触发YTO AI交互时发生错误", e)
            config.incrementFailureCount()

            Messages.showErrorDialog(
                project,
                "执行YTO AI交互时发生错误: ${e.message}",
                "执行失败"
            )
        }
    }
    

    
    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }
}
