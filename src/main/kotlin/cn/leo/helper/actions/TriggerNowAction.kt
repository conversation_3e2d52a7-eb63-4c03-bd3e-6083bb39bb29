package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.ui.Messages
import cn.leo.helper.AIPluginDetector
import cn.leo.helper.AIQuestionGenerator
import cn.leo.helper.KpiHelperConfig
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.impl.SimpleDataContext
import com.intellij.openapi.diagnostic.Logger

class TriggerNowAction : AnAction() {
    
    private val logger = Logger.getInstance(TriggerNowAction::class.java)
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            Messages.showErrorDialog("没有打开的项目", "错误")
            return
        }
        
        val config = KpiHelperConfig.getInstance()
        
        try {
            // 检测AI插件
            val detector = AIPluginDetector()
            val aiActions = detector.detectAIPlugins()
            
            if (aiActions.isEmpty()) {
                Messages.showWarningDialog(
                    project,
                    "未检测到任何AI插件。请确保已安装并启用AI编程助手插件。",
                    "警告"
                )
                return
            }
            
            // 生成问题
            val generator = AIQuestionGenerator()
            val question = generator.generateQuestion()
            
            // 选择AI插件
            val selectedAction = aiActions.random()
            
            // 执行AI交互
            executeAIInteraction(selectedAction, question, project)
            
            // 更新统计
            config.incrementInteractionCount()
            config.incrementSuccessCount()
            
            Messages.showInfoMessage(
                project,
                "已成功触发AI交互！\n" +
                "使用插件: $selectedAction\n" +
                "问题: $question",
                "执行成功"
            )
            
        } catch (e: Exception) {
            logger.error("手动触发AI交互时发生错误", e)
            config.incrementFailureCount()
            
            Messages.showErrorDialog(
                project,
                "执行AI交互时发生错误: ${e.message}",
                "执行失败"
            )
        }
    }
    
    private fun executeAIInteraction(actionId: String, question: String, project: com.intellij.openapi.project.Project) {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction(actionId)
        
        if (action == null) {
            throw IllegalArgumentException("Action '$actionId' 未找到")
        }
        
        ApplicationManager.getApplication().invokeLater {
            try {
                val dataContext = SimpleDataContext.getProjectContext(project)
                val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.ManualTrigger", dataContext)
                
                action.actionPerformed(event)
                logger.info("手动触发AI插件动作成功: $actionId")
                
                // 模拟用户输入（延迟执行）
                val config = KpiHelperConfig.getInstance()
                if (config.simulateUserInput) {
                    ApplicationManager.getApplication().executeOnPooledThread {
                        try {
                            Thread.sleep(config.inputDelaySeconds * 1000L)
                            // 这里可以添加具体的文本输入逻辑
                            logger.info("模拟用户输入: $question")
                        } catch (e: Exception) {
                            logger.warn("模拟用户输入失败", e)
                        }
                    }
                }
                
            } catch (e: Exception) {
                logger.error("执行AI动作时发生错误: $actionId", e)
                throw e
            }
        }
    }
    
    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }
}
