package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages
import cn.leo.helper.KpiHelperConfig

class OpenConfigAction : AnAction() {
    
    override fun actionPerformed(e: AnActionEvent) {
        val config = KpiHelperConfig.getInstance()
        
        // 简单的配置对话框（后续可以扩展为更复杂的UI）
        val currentSettings = buildString {
            appendLine("当前配置:")
            appendLine("启用状态: ${config.enabled}")
            appendLine("执行间隔: ${config.minIntervalMinutes}-${config.maxIntervalMinutes} 分钟")
            appendLine("初始延迟: ${config.initialDelayMinutes} 分钟")
            appendLine("最大重试: ${config.maxRetries} 次")
            appendLine("工作时间限制: ${config.enableWorkingHoursOnly}")
            if (config.enableWorkingHoursOnly) {
                appendLine("工作时间: ${config.workingHoursStart}:00-${config.workingHoursEnd}:00")
            }
            appendLine("仅工作日: ${config.enableWeekdaysOnly}")
            appendLine("模拟用户输入: ${config.simulateUserInput}")
            appendLine("自定义问题数量: ${config.customQuestions.size}")
        }
        
        val options = arrayOf("重置为默认", "查看详细配置", "关闭")
        val choice = Messages.showDialog(
            e.project,
            currentSettings,
            "配置设置",
            options,
            2,
            Messages.getInformationIcon()
        )
        
        when (choice) {
            0 -> { // 重置为默认
                val confirm = Messages.showYesNoDialog(
                    e.project,
                    "确定要重置所有配置为默认值吗？这将清除所有自定义设置。",
                    "确认重置",
                    Messages.getQuestionIcon()
                )
                
                if (confirm == Messages.YES) {
                    config.resetToDefaults()
                    Messages.showInfoMessage(
                        e.project,
                        "配置已重置为默认值",
                        "重置完成"
                    )
                }
            }
            
            1 -> { // 查看详细配置
                showDetailedConfig(e, config)
            }
            
            // 2 或其他 -> 关闭，不做任何操作
        }
    }
    
    private fun showDetailedConfig(e: AnActionEvent, config: KpiHelperConfig) {
        val detailedInfo = buildString {
            appendLine("=== 详细配置信息 ===")
            appendLine()
            
            appendLine("基本设置:")
            appendLine("• 功能启用: ${config.enabled}")
            appendLine("• 自动检测AI插件: ${config.autoDetectAIPlugins}")
            appendLine("• 启用日志: ${config.enableLogging}")
            appendLine("• 日志级别: ${config.logLevel}")
            appendLine()
            
            appendLine("执行设置:")
            appendLine("• 最小间隔: ${config.minIntervalMinutes} 分钟")
            appendLine("• 最大间隔: ${config.maxIntervalMinutes} 分钟")
            appendLine("• 初始延迟: ${config.initialDelayMinutes} 分钟")
            appendLine("• 最大重试次数: ${config.maxRetries}")
            appendLine("• 模拟用户输入: ${config.simulateUserInput}")
            appendLine("• 输入延迟: ${config.inputDelaySeconds} 秒")
            appendLine()
            
            appendLine("时间限制:")
            appendLine("• 仅工作时间: ${config.enableWorkingHoursOnly}")
            appendLine("• 工作开始时间: ${config.workingHoursStart}:00")
            appendLine("• 工作结束时间: ${config.workingHoursEnd}:00")
            appendLine("• 仅工作日: ${config.enableWeekdaysOnly}")
            appendLine()
            
            appendLine("问题生成:")
            appendLine("• 启用随机问题: ${config.enableRandomQuestions}")
            appendLine("• 问题类型: ${config.questionTypes.joinToString(", ")}")
            appendLine("• 自定义问题数量: ${config.customQuestions.size}")
            if (config.customQuestions.isNotEmpty()) {
                appendLine("• 自定义问题:")
                config.customQuestions.forEachIndexed { index, question ->
                    appendLine("  ${index + 1}. $question")
                }
            }
            appendLine()
            
            appendLine("AI插件设置:")
            appendLine("• 首选插件数量: ${config.preferredAIPlugins.size}")
            if (config.preferredAIPlugins.isNotEmpty()) {
                appendLine("• 首选插件:")
                config.preferredAIPlugins.forEach { plugin ->
                    appendLine("  - $plugin")
                }
            }
            appendLine("• 自定义Action ID数量: ${config.customAIActionIds.size}")
            if (config.customAIActionIds.isNotEmpty()) {
                appendLine("• 自定义Action ID:")
                config.customAIActionIds.forEach { actionId ->
                    appendLine("  - $actionId")
                }
            }
            appendLine()
            
            appendLine("统计信息:")
            appendLine("• 总交互次数: ${config.totalInteractions}")
            appendLine("• 成功次数: ${config.successfulInteractions}")
            appendLine("• 失败次数: ${config.failedInteractions}")
            appendLine("• 成功率: ${String.format("%.1f%%", config.getSuccessRate() * 100)}")
            
            if (config.lastInteractionTime > 0) {
                val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                appendLine("• 最后执行时间: ${formatter.format(java.util.Date(config.lastInteractionTime))}")
            }
            
            // 配置验证
            val errors = config.validateConfig()
            if (errors.isNotEmpty()) {
                appendLine()
                appendLine("配置错误:")
                errors.forEach { error ->
                    appendLine("• $error")
                }
            }
        }
        
        Messages.showInfoMessage(
            e.project,
            detailedInfo,
            "详细配置"
        )
    }
}
