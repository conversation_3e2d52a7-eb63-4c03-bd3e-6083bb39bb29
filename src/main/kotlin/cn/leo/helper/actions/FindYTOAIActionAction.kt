package cn.leo.helper.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.ui.Messages
import cn.leo.helper.YTOAIPluginHelper
import cn.leo.helper.KpiHelperConfig

class FindYTOAIActionAction : AnAction() {
    
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        if (project == null) {
            Messages.showErrorDialog("没有打开的项目", "错误")
            return
        }
        
        val helper = YTOAIPluginHelper()
        val config = KpiHelperConfig.getInstance()
        
        // 查找YTO AI插件的Action ID
        val foundActionId = helper.findYTOAIActionId()
        
        if (foundActionId != null) {
            // 找到了Action ID，询问是否保存到配置
            val message = "找到YTO AI插件的Action ID:\n\n$foundActionId\n\n是否将其保存到配置中？"
            val result = Messages.showYesNoDialog(
                project,
                message,
                "找到YTO AI插件",
                Messages.getQuestionIcon()
            )
            
            if (result == Messages.YES) {
                config.addPreferredAIPlugin(foundActionId)
                Messages.showInfoMessage(
                    project,
                    "Action ID已保存到配置中：$foundActionId",
                    "保存成功"
                )
            }
        } else {
            // 没找到，显示所有可能相关的Action
            val allActions = helper.listAllPossibleActions()
            
            if (allActions.isNotEmpty()) {
                val actionList = allActions.joinToString("\n")
                val message = "未自动找到YTO AI插件，但发现以下可能相关的Action：\n\n$actionList\n\n" +
                            "请手动选择正确的Action ID并在配置中添加。"
                
                Messages.showInfoMessage(
                    project,
                    message,
                    "需要手动配置"
                )
            } else {
                val message = "未找到任何相关的Action。\n\n" +
                            "请确保：\n" +
                            "1. YTO AI插件已正确安装\n" +
                            "2. 插件已启用\n" +
                            "3. 重启IDE后再试\n\n" +
                            "您也可以手动在配置中添加Action ID。"
                
                Messages.showWarningDialog(
                    project,
                    message,
                    "未找到插件"
                )
            }
        }
    }
    
    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = e.project != null
    }
}
