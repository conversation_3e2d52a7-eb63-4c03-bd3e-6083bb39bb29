package cn.leo.helper

import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.diagnostic.Logger

class YTOAIPluginHelper {

    private val logger = Logger.getInstance(YTOAIPluginHelper::class.java)

    /**
     * 查找YTO AI插件的Action ID
     * 这个方法会尝试多种策略来找到正确的Action ID
     */
    fun findYTOAIActionId(): String? {
        logger.info("KpiHelper: 开始查找YTO AI插件的Action ID")

        // 策略1: 检查已安装的插件中是否有YTO相关的插件
        val ytoPluginActions = findYTOPluginActions()
        if (ytoPluginActions.isNotEmpty()) {
            val selectedAction = ytoPluginActions.first()
            logger.info("KpiHelper: 通过插件检测找到YTO AI Action: $selectedAction")
            return selectedAction
        }

        // 策略2: 扫描所有Action，查找YTO相关的
        val ytoActions = scanForYTOActions()
        if (ytoActions.isNotEmpty()) {
            val selectedAction = ytoActions.first()
            logger.info("KpiHelper: 通过Action扫描找到YTO AI Action: $selectedAction")
            return selectedAction
        }

        // 策略3: 查找通用的AI相关Action
        val aiActions = scanForGenericAIActions()
        if (aiActions.isNotEmpty()) {
            val selectedAction = aiActions.first()
            logger.info("KpiHelper: 找到通用AI Action: $selectedAction")
            return selectedAction
        }

        logger.warn("KpiHelper: 未找到任何YTO AI插件的Action ID")
        return null
    }

    /**
     * 检查已安装的插件，查找YTO相关的插件
     */
    private fun findYTOPluginActions(): List<String> {
        val actions = mutableListOf<String>()

        try {
            val installedPlugins = PluginManagerCore.plugins

            for (plugin in installedPlugins.toList()) {
                if (!plugin.isEnabled) continue

                val pluginId = plugin.pluginId.idString.lowercase()
                val pluginName = plugin.name.lowercase()

                // 检查是否是YTO相关的插件
                val isYTOPlugin = pluginId.contains("yto") ||
                        pluginName.contains("yto") ||
                        pluginId.contains("aicode") ||
                        pluginName.contains("aicode")

                if (isYTOPlugin) {
                    logger.info("KpiHelper: 发现YTO插件: ${plugin.name} (${plugin.pluginId})")

                    // 查找该插件的Action
                    val pluginActions = findActionsForPlugin(plugin.pluginId.idString)
                    actions.addAll(pluginActions)
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 查找YTO插件时发生错误", e)
        }

        return actions
    }

    /**
     * 扫描所有Action，查找YTO相关的
     */
    private fun scanForYTOActions(): List<String> {
        val actions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            val ytoKeywords = listOf("yto", "ytoai", "aicode")

            for (actionId in allActionIds) {
                val actionIdLower = actionId.lowercase()

                val isYTOAction = ytoKeywords.any { keyword ->
                    actionIdLower.contains(keyword)
                }

                if (isYTOAction) {
                    val action = actionManager.getAction(actionId)
                    if (action != null) {
                        actions.add(actionId)
                        logger.info("KpiHelper: 发现YTO Action: $actionId")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 扫描YTO Action时发生错误", e)
        }

        return actions
    }

    /**
     * 查找通用的AI相关Action
     */
    private fun scanForGenericAIActions(): List<String> {
        val actions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            val aiKeywords = listOf("ai.chat", "ai.assistant", "chat", "assistant")

            for (actionId in allActionIds) {
                val actionIdLower = actionId.lowercase()

                val isAIAction = aiKeywords.any { keyword ->
                    actionIdLower.contains(keyword)
                }

                if (isAIAction) {
                    val action = actionManager.getAction(actionId)
                    if (action != null && isInteractiveAction(actionId)) {
                        actions.add(actionId)
                        logger.info("KpiHelper: 发现通用AI Action: $actionId")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 扫描通用AI Action时发生错误", e)
        }

        return actions
    }

    /**
     * 查找特定插件的Action
     */
    private fun findActionsForPlugin(pluginId: String): List<String> {
        val actions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            for (actionId in allActionIds) {
                // 检查Action ID是否属于该插件
                if (actionId.startsWith(pluginId) ||
                    actionId.contains(pluginId.split(".").last())
                ) {

                    val action = actionManager.getAction(actionId)
                    if (action != null && isInteractiveAction(actionId)) {
                        actions.add(actionId)
                        logger.info("KpiHelper: 插件 $pluginId 的Action: $actionId")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 查找插件 $pluginId 的Action时发生错误", e)
        }

        return actions
    }

    /**
     * 判断是否是交互式的Action（可能是聊天或对话相关的）
     */
    private fun isInteractiveAction(actionId: String): Boolean {
        val actionIdLower = actionId.lowercase()

        // 优先选择包含交互关键词的Action
        val interactionKeywords = listOf(
            "chat", "ask", "query", "question", "explain", "generate",
            "complete", "suggest", "open", "show", "start", "dialog"
        )

        // 排除设置和配置相关的Action
        val excludeKeywords = listOf(
            "settings", "config", "preference", "install", "update",
            "disable", "enable", "uninstall", "about", "help"
        )

        val hasInteractionKeyword = interactionKeywords.any { keyword ->
            actionIdLower.contains(keyword)
        }

        val hasExcludeKeyword = excludeKeywords.any { keyword ->
            actionIdLower.contains(keyword)
        }

        return hasInteractionKeyword && !hasExcludeKeyword
    }

    /**
     * 列出所有可能相关的Action，用于调试
     */
    fun listAllPossibleActions(): List<String> {
        val relevantActions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            logger.info("KpiHelper: 列出所有可能相关的Action (总共${allActionIds.size}个)")

            for (actionId in allActionIds) {
                val actionIdLower = actionId.lowercase()

                val isRelevant = actionIdLower.contains("yto") ||
                        actionIdLower.contains("ai") ||
                        actionIdLower.contains("chat") ||
                        actionIdLower.contains("assistant") ||
                        actionIdLower.contains("ask") ||
                        actionIdLower.contains("generate") ||
                        actionIdLower.contains("explain") ||
                        actionIdLower.contains("complete")

                if (isRelevant) {
                    relevantActions.add(actionId)
                    logger.info("KpiHelper: 相关Action: $actionId")
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 列出Action时发生错误", e)
        }

        return relevantActions
    }

    /**
     * 验证Action ID是否有效
     */
    fun validateActionId(actionId: String): Boolean {
        return try {
            val actionManager = ActionManager.getInstance()
            val action = actionManager.getAction(actionId)
            action != null
        } catch (e: Exception) {
            logger.error("KpiHelper: 验证Action ID时发生错误: $actionId", e)
            false
        }
    }
}
