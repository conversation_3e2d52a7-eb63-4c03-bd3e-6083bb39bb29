package cn.leo.helper

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.impl.SimpleDataContext
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager
import java.awt.Component
import java.awt.Robot
import java.awt.event.KeyEvent
import java.awt.Toolkit
import java.awt.datatransfer.StringSelection
import javax.swing.JButton
import javax.swing.JComponent
import javax.swing.text.JTextComponent

/**
 * YTO AI插件交互器
 * 专门负责与YTO AI插件进行交互
 */
class YTOAIInteractor(private val project: Project) {
    
    private val logger = Logger.getInstance(YTOAIInteractor::class.java)
    private val config = KpiHelperConfig.getInstance()
    
    /**
     * 执行与YTO AI插件的交互
     */
    fun executeInteraction(question: String): Boolean {
        logger.info("KpiHelper: 开始与YTO AI插件交互，问题: $question")

        // 策略1: 优先尝试通过Tool Window触发（最可靠）
        if (tryToolWindowApproach(question)) {
            logger.info("KpiHelper: 通过Tool Window成功触发YTO AI插件")
            return true
        }

        // 策略2: 尝试通过Action触发
        if (tryActionApproach(question)) {
            logger.info("KpiHelper: 通过Action成功触发YTO AI插件")
            return true
        }

        // 策略3: 尝试通过键盘快捷键触发
        if (tryKeyboardShortcut(question)) {
            logger.info("KpiHelper: 通过键盘快捷键成功触发YTO AI插件")
            return true
        }

        logger.warn("KpiHelper: 所有方法都失败，无法触发YTO AI插件")
        return false
    }
    
    /**
     * 策略1: 通过Action触发
     */
    private fun tryActionApproach(question: String): Boolean {
        return try {
            val helper = YTOAIPluginHelper()
            val actionId = helper.findYTOAIActionId()
            
            if (actionId != null) {
                executeAction(actionId, question)
                true
            } else {
                logger.warn("KpiHelper: 未找到YTO AI插件的Action ID")
                false
            }
        } catch (e: Exception) {
            logger.error("KpiHelper: Action方式失败", e)
            false
        }
    }
    
    /**
     * 策略1: 通过Tool Window触发（优先策略）
     */
    private fun tryToolWindowApproach(question: String): Boolean {
        return try {
            logger.info("KpiHelper: 尝试通过Tool Window激活YTO AI插件")

            val toolWindowManager = ToolWindowManager.getInstance(project)
            val toolWindow = toolWindowManager.getToolWindow("YtoAICode")

            if (toolWindow == null) {
                logger.warn("KpiHelper: 未找到YtoAICode Tool Window，请确保YTO AI插件已安装")
                return false
            }

            if (!toolWindow.isAvailable) {
                logger.warn("KpiHelper: YtoAICode Tool Window不可用")
                return false
            }

            // 激活Tool Window
            var success = false
            val latch = java.util.concurrent.CountDownLatch(1)

            ApplicationManager.getApplication().invokeLater {
                try {
                    // 显示Tool Window
                    toolWindow.show {
                        logger.info("KpiHelper: YtoAICode Tool Window已显示")

                        // 激活Tool Window
                        toolWindow.activate {
                            logger.info("KpiHelper: YtoAICode Tool Window已激活")

                            // 延迟执行交互，让界面完全加载
                            ApplicationManager.getApplication().executeOnPooledThread {
                                try {
                                    Thread.sleep(config.inputDelaySeconds * 1000L + 1000L) // 额外1秒等待

                                    // 尝试直接输入问题
                                    success = directInputToToolWindow(question)

                                    if (!success) {
                                        // 如果直接输入失败，尝试组件交互
                                        success = interactWithToolWindow(toolWindow, question)
                                    }

                                } catch (e: Exception) {
                                    logger.error("KpiHelper: Tool Window交互失败", e)
                                } finally {
                                    latch.countDown()
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    logger.error("KpiHelper: 激活Tool Window失败", e)
                    latch.countDown()
                }
            }

            // 等待交互完成
            latch.await(15, java.util.concurrent.TimeUnit.SECONDS)
            success

        } catch (e: Exception) {
            logger.error("KpiHelper: Tool Window方式失败", e)
            false
        }
    }

    /**
     * 直接输入到Tool Window（使用剪贴板和键盘）
     */
    private fun directInputToToolWindow(question: String): Boolean {
        return try {
            logger.info("KpiHelper: 尝试直接输入到Tool Window")

            // 等待一下确保Tool Window获得焦点
            Thread.sleep(1000)

            // 使用剪贴板输入问题
            if (inputQuestionViaClipboard(question)) {
                logger.info("KpiHelper: 成功通过剪贴板输入问题到Tool Window")
                return true
            }

            false
        } catch (e: Exception) {
            logger.error("KpiHelper: 直接输入到Tool Window失败", e)
            false
        }
    }
    
    /**
     * 策略3: 通过键盘快捷键触发
     */
    private fun tryKeyboardShortcut(question: String): Boolean {
        return try {
            logger.info("KpiHelper: 尝试通过键盘快捷键触发YTO AI插件")
            
            val robot = Robot()
            
            // 常见的AI助手快捷键组合
            val shortcuts = listOf(
                listOf(KeyEvent.VK_CONTROL, KeyEvent.VK_SHIFT, KeyEvent.VK_A), // Ctrl+Shift+A
                listOf(KeyEvent.VK_ALT, KeyEvent.VK_A), // Alt+A
                listOf(KeyEvent.VK_CONTROL, KeyEvent.VK_ALT, KeyEvent.VK_A), // Ctrl+Alt+A
            )
            
            for (shortcut in shortcuts) {
                try {
                    logger.info("KpiHelper: 尝试快捷键: ${shortcut.joinToString("+")}")
                    
                    // 按下快捷键
                    shortcut.forEach { robot.keyPress(it) }
                    Thread.sleep(100)
                    shortcut.reversed().forEach { robot.keyRelease(it) }
                    
                    Thread.sleep(1000) // 等待界面响应
                    
                    // 尝试输入问题
                    if (inputQuestionViaClipboard(question)) {
                        return true
                    }
                    
                } catch (e: Exception) {
                    logger.warn("KpiHelper: 快捷键 ${shortcut.joinToString("+")} 失败", e)
                }
            }
            
            false
        } catch (e: Exception) {
            logger.error("KpiHelper: 键盘快捷键方式失败", e)
            false
        }
    }
    
    /**
     * 执行指定的Action
     */
    private fun executeAction(actionId: String, question: String) {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction(actionId)
        
        if (action == null) {
            throw IllegalArgumentException("Action '$actionId' 未找到")
        }
        
        ApplicationManager.getApplication().invokeLater {
            try {
                val dataContext = SimpleDataContext.getProjectContext(project)
                val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.YTOInteraction", dataContext)
                
                action.actionPerformed(event)
                logger.info("KpiHelper: 成功执行Action: $actionId")
                
                // 延迟输入问题
                if (config.simulateUserInput) {
                    ApplicationManager.getApplication().executeOnPooledThread {
                        try {
                            Thread.sleep(config.inputDelaySeconds * 1000L)
                            inputQuestionViaClipboard(question)
                        } catch (e: Exception) {
                            logger.warn("KpiHelper: 输入问题失败", e)
                        }
                    }
                }
                
            } catch (e: Exception) {
                logger.error("KpiHelper: 执行Action失败: $actionId", e)
                throw e
            }
        }
    }
    
    /**
     * 与Tool Window进行交互
     */
    private fun interactWithToolWindow(toolWindow: com.intellij.openapi.wm.ToolWindow, question: String): Boolean {
        return try {
            val contentManager = toolWindow.contentManager
            val content = contentManager.selectedContent
            
            if (content != null) {
                val component = content.component
                logger.info("KpiHelper: 找到Tool Window内容组件: ${component.javaClass.simpleName}")
                
                // 查找并与组件交互
                val inputFound = findInputComponent(component, question)
                val buttonFound = findSubmitButton(component)
                
                inputFound || buttonFound
            } else {
                logger.warn("KpiHelper: Tool Window没有内容")
                false
            }
            
        } catch (e: Exception) {
            logger.error("KpiHelper: Tool Window交互失败", e)
            false
        }
    }
    
    /**
     * 查找并操作输入组件
     */
    private fun findInputComponent(component: Component, question: String): Boolean {
        var found = false
        
        try {
            when (component) {
                is JTextComponent -> {
                    logger.info("KpiHelper: 找到文本输入组件")
                    ApplicationManager.getApplication().invokeLater {
                        try {
                            component.text = question
                            logger.info("KpiHelper: 已输入问题: $question")
                            found = true
                        } catch (e: Exception) {
                            logger.error("KpiHelper: 输入问题失败", e)
                        }
                    }
                }
                is JComponent -> {
                    for (i in 0 until component.componentCount) {
                        if (findInputComponent(component.getComponent(i), question)) {
                            found = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("KpiHelper: 查找输入组件时发生错误", e)
        }
        
        return found
    }
    
    /**
     * 查找并点击提交按钮
     */
    private fun findSubmitButton(component: Component): Boolean {
        var found = false
        
        try {
            when (component) {
                is JButton -> {
                    val buttonText = component.text?.lowercase() ?: ""
                    val isSubmitButton = buttonText.contains("发送") || 
                                       buttonText.contains("send") || 
                                       buttonText.contains("submit") ||
                                       buttonText.contains("ask") ||
                                       buttonText.contains("提交")
                    
                    if (isSubmitButton) {
                        logger.info("KpiHelper: 找到发送按钮: ${component.text}")
                        ApplicationManager.getApplication().invokeLater {
                            try {
                                component.doClick()
                                logger.info("KpiHelper: 已点击发送按钮")
                                found = true
                            } catch (e: Exception) {
                                logger.error("KpiHelper: 点击发送按钮失败", e)
                            }
                        }
                    }
                }
                is JComponent -> {
                    for (i in 0 until component.componentCount) {
                        if (findSubmitButton(component.getComponent(i))) {
                            found = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("KpiHelper: 查找提交按钮时发生错误", e)
        }
        
        return found
    }
    
    /**
     * 通过剪贴板输入问题
     */
    private fun inputQuestionViaClipboard(question: String): Boolean {
        return try {
            // 将问题复制到剪贴板
            val clipboard = Toolkit.getDefaultToolkit().systemClipboard
            val selection = StringSelection(question)
            clipboard.setContents(selection, null)
            
            // 使用Ctrl+V粘贴
            val robot = Robot()
            robot.keyPress(KeyEvent.VK_CONTROL)
            robot.keyPress(KeyEvent.VK_V)
            Thread.sleep(100)
            robot.keyRelease(KeyEvent.VK_V)
            robot.keyRelease(KeyEvent.VK_CONTROL)
            
            Thread.sleep(500)
            
            // 按Enter发送
            robot.keyPress(KeyEvent.VK_ENTER)
            robot.keyRelease(KeyEvent.VK_ENTER)
            
            logger.info("KpiHelper: 通过剪贴板成功输入问题: $question")
            true
            
        } catch (e: Exception) {
            logger.error("KpiHelper: 通过剪贴板输入问题失败", e)
            false
        }
    }
}
