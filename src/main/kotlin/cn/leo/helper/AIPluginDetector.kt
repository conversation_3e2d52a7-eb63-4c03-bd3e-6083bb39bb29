package cn.leo.helper

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.openapi.extensions.PluginId

class AIPluginDetector {

    private val logger = Logger.getInstance(AIPluginDetector::class.java)

    // 公司AI插件的可能Action ID模式
    private val companyAIActionPatterns = listOf(
        // YTO AI相关
        "yto",
        "ytoai",
        "yto.ai",
        "ytoaicode",
        "yto.aicode",

        // 可能的通用模式
        "ai.chat",
        "ai.code",
        "ai.assistant",
        "assistant",
        "chat",
        "ask",
        "query",
        "generate",
        "explain",
        "complete"
    )
    
    // 公司AI插件相关的Action关键词
    private val companyAIActionKeywords = listOf(
        "yto", "ytoai", "aicode", "chat", "ask", "explain",
        "generate", "complete", "suggest", "assistant", "ai"
    )
    
    fun detectAIPlugins(): List<String> {
        val detectedActions = mutableListOf<String>()

        try {
            // 优先检测公司的YTO AI插件
            detectedActions.addAll(detectYTOAIPlugin())

            // 如果没找到，再用通用方法检测
            if (detectedActions.isEmpty()) {
                logger.info("KpiHelper: 未找到YTO AI插件，尝试通用检测方法")
                detectedActions.addAll(detectByInstalledPlugins())
                detectedActions.addAll(detectByActionScan())
            }

            // 去重并返回
            val uniqueActions = detectedActions.distinct()
            logger.info("KpiHelper: 检测到 ${uniqueActions.size} 个AI相关Action: $uniqueActions")

            return uniqueActions

        } catch (e: Exception) {
            logger.error("KpiHelper: 检测AI插件时发生错误", e)
            return emptyList()
        }
    }

    private fun detectYTOAIPlugin(): List<String> {
        val actions = mutableListOf<String>()

        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            logger.info("KpiHelper: 开始检测YTO AI插件，总共有 ${allActionIds.size} 个Action")

            // 专门查找YTO相关的Action
            for (actionId in allActionIds) {
                val actionIdLower = actionId.lowercase()

                // 检查是否包含YTO相关关键词
                val isYTOAction = companyAIActionPatterns.any { pattern ->
                    actionIdLower.contains(pattern.lowercase())
                }

                if (isYTOAction) {
                    val action = actionManager.getAction(actionId)
                    if (action != null) {
                        actions.add(actionId)
                        logger.info("KpiHelper: 发现YTO AI Action: $actionId")
                    }
                }
            }

            // 如果没找到YTO特定的Action，尝试查找通用的AI Action
            if (actions.isEmpty()) {
                logger.info("KpiHelper: 未找到YTO特定Action，查找通用AI Action")
                for (actionId in allActionIds) {
                    val actionIdLower = actionId.lowercase()

                    val isAIAction = companyAIActionKeywords.any { keyword ->
                        actionIdLower.contains(keyword)
                    }

                    if (isAIAction) {
                        val action = actionManager.getAction(actionId)
                        if (action != null && isValidAIAction(actionId, action)) {
                            actions.add(actionId)
                            logger.info("KpiHelper: 发现通用AI Action: $actionId")
                        }
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 检测YTO AI插件时发生错误", e)
        }

        return actions
    }
    
    private fun detectByInstalledPlugins(): List<String> {
        val actions = mutableListOf<String>()
        
        try {
            val installedPlugins = PluginManagerCore.getPlugins()

            for (plugin in installedPlugins.toList()) {
                if (!plugin.isEnabled) continue
                
                val pluginId = plugin.pluginId.idString.lowercase()
                val pluginName = plugin.name.lowercase()
                
                // 检查插件ID和名称是否包含AI相关关键词
                val isAIPlugin = companyAIActionPatterns.any { pattern ->
                    pluginId.contains(pattern.lowercase()) || pluginName.contains(pattern.lowercase())
                }
                
                if (isAIPlugin) {
                    logger.info("KpiHelper: 发现AI插件: ${plugin.name} (${plugin.pluginId})")
                    
                    // 尝试查找该插件的相关Action
                    actions.addAll(findActionsForPlugin(plugin.pluginId))
                }
            }
            
        } catch (e: Exception) {
            logger.warn("KpiHelper: 通过已安装插件检测AI Action时发生错误", e)
        }
        
        return actions
    }
    
    private fun detectByActionScan(): List<String> {
        val actions = mutableListOf<String>()
        
        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")
            
            for (actionId in allActionIds) {
                val action = actionManager.getAction(actionId)
                if (action == null) continue
                
                // 检查Action ID是否包含公司AI相关关键词
                val actionIdLower = actionId.lowercase()
                val isAIAction = companyAIActionKeywords.any { keyword ->
                    actionIdLower.contains(keyword)
                }
                
                if (isAIAction) {
                    // 进一步验证这是否真的是一个AI相关的Action
                    if (isValidAIAction(actionId, action)) {
                        actions.add(actionId)
                        logger.debug("KpiHelper: 发现AI Action: $actionId")
                    }
                }
            }
            
        } catch (e: Exception) {
            logger.warn("KpiHelper: 通过Action扫描检测AI Action时发生错误", e)
        }
        
        return actions
    }
    
    private fun findActionsForPlugin(pluginId: PluginId): List<String> {
        val actions = mutableListOf<String>()
        
        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")
            
            for (actionId in allActionIds) {
                // 检查Action ID是否属于该插件
                if (actionId.startsWith(pluginId.idString) || 
                    actionId.contains(pluginId.idString.split(".").last())) {
                    
                    val action = actionManager.getAction(actionId)
                    if (action != null && isValidAIAction(actionId, action)) {
                        actions.add(actionId)
                    }
                }
            }
            
        } catch (e: Exception) {
            logger.warn("KpiHelper: 查找插件 $pluginId 的Action时发生错误", e)
        }
        
        return actions
    }
    
    private fun isValidAIAction(actionId: String, action: Any): Boolean {
        try {
            // 检查Action的类名和包名
            val actionClass = action.javaClass
            val className = actionClass.name.lowercase()
            val packageName = actionClass.packageName?.lowercase() ?: ""
            
            // 排除一些明显不是AI交互的Action
            val excludePatterns = listOf(
                "settings", "config", "preference", "install", "update",
                "disable", "enable", "uninstall", "about", "help"
            )
            
            val actionIdLower = actionId.lowercase()
            val isExcluded = excludePatterns.any { pattern ->
                actionIdLower.contains(pattern)
            }
            
            if (isExcluded) return false
            
            // 优先选择包含交互关键词的Action
            val interactionKeywords = listOf(
                "chat", "ask", "query", "question", "explain", "generate",
                "complete", "suggest", "open", "show", "start"
            )
            
            val hasInteractionKeyword = interactionKeywords.any { keyword ->
                actionIdLower.contains(keyword) || className.contains(keyword)
            }
            
            return hasInteractionKeyword
            
        } catch (e: Exception) {
            logger.debug("KpiHelper: 验证Action $actionId 时发生错误", e)
            return false
        }
    }
    
    fun getPreferredAIActions(): List<String> {
        // 返回按优先级排序的AI Action列表
        val detectedActions = detectAIPlugins()
        
        // 定义优先级顺序
        val priorityPatterns = listOf(
            "chat", "ask", "open", "show", "start", "generate", "explain"
        )
        
        return detectedActions.sortedBy { actionId ->
            val actionIdLower = actionId.lowercase()
            priorityPatterns.indexOfFirst { pattern ->
                actionIdLower.contains(pattern)
            }.let { if (it == -1) Int.MAX_VALUE else it }
        }
    }
}
