package cn.leo.helper

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.extensions.PluginManager
import com.intellij.openapi.extensions.PluginId

class AIPluginDetector {
    
    private val logger = Logger.getInstance(AIPluginDetector::class.java)
    
    // 常见AI编程助手插件的Action ID模式
    private val knownAIActionPatterns = listOf(
        // GitHub Copilot
        "com.github.copilot",
        "copilot",
        
        // Tabnine
        "com.tabnine",
        "tabnine",
        
        // Codeium
        "com.codeium",
        "codeium",
        
        // CodeGPT
        "codegpt",
        "ee.carlrobert.codegpt",
        
        // AI Assistant
        "ai.assistant",
        "aiassistant",
        
        // JetBrains AI Assistant
        "com.intellij.ml.llm",
        "jetbrains.ai",
        
        // 其他常见模式
        "ai.chat",
        "ai.code",
        "assistant",
        "gpt",
        "llm",
        "chat"
    )
    
    // 常见的AI相关Action关键词
    private val aiActionKeywords = listOf(
        "chat", "ask", "explain", "generate", "complete", 
        "suggest", "assistant", "ai", "gpt", "copilot",
        "tabnine", "codeium", "llm"
    )
    
    fun detectAIPlugins(): List<String> {
        val detectedActions = mutableListOf<String>()
        
        try {
            // 方法1: 检查已安装的插件
            detectedActions.addAll(detectByInstalledPlugins())
            
            // 方法2: 扫描所有注册的Action
            detectedActions.addAll(detectByActionScan())
            
            // 去重并返回
            val uniqueActions = detectedActions.distinct()
            logger.info("KpiHelper: 检测到 ${uniqueActions.size} 个AI相关Action: $uniqueActions")
            
            return uniqueActions
            
        } catch (e: Exception) {
            logger.error("KpiHelper: 检测AI插件时发生错误", e)
            return emptyList()
        }
    }
    
    private fun detectByInstalledPlugins(): List<String> {
        val actions = mutableListOf<String>()
        
        try {
            val pluginManager = PluginManager.getInstance()
            val installedPlugins = pluginManager.plugins
            
            for (plugin in installedPlugins) {
                if (!plugin.isEnabled) continue
                
                val pluginId = plugin.pluginId.idString.lowercase()
                val pluginName = plugin.name.lowercase()
                
                // 检查插件ID和名称是否包含AI相关关键词
                val isAIPlugin = knownAIActionPatterns.any { pattern ->
                    pluginId.contains(pattern.lowercase()) || pluginName.contains(pattern.lowercase())
                }
                
                if (isAIPlugin) {
                    logger.info("KpiHelper: 发现AI插件: ${plugin.name} (${plugin.pluginId})")
                    
                    // 尝试查找该插件的相关Action
                    actions.addAll(findActionsForPlugin(plugin.pluginId))
                }
            }
            
        } catch (e: Exception) {
            logger.warn("KpiHelper: 通过已安装插件检测AI Action时发生错误", e)
        }
        
        return actions
    }
    
    private fun detectByActionScan(): List<String> {
        val actions = mutableListOf<String>()
        
        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")
            
            for (actionId in allActionIds) {
                val action = actionManager.getAction(actionId)
                if (action == null) continue
                
                // 检查Action ID是否包含AI相关关键词
                val actionIdLower = actionId.lowercase()
                val isAIAction = aiActionKeywords.any { keyword ->
                    actionIdLower.contains(keyword)
                }
                
                if (isAIAction) {
                    // 进一步验证这是否真的是一个AI相关的Action
                    if (isValidAIAction(actionId, action)) {
                        actions.add(actionId)
                        logger.debug("KpiHelper: 发现AI Action: $actionId")
                    }
                }
            }
            
        } catch (e: Exception) {
            logger.warn("KpiHelper: 通过Action扫描检测AI Action时发生错误", e)
        }
        
        return actions
    }
    
    private fun findActionsForPlugin(pluginId: PluginId): List<String> {
        val actions = mutableListOf<String>()
        
        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")
            
            for (actionId in allActionIds) {
                // 检查Action ID是否属于该插件
                if (actionId.startsWith(pluginId.idString) || 
                    actionId.contains(pluginId.idString.split(".").last())) {
                    
                    val action = actionManager.getAction(actionId)
                    if (action != null && isValidAIAction(actionId, action)) {
                        actions.add(actionId)
                    }
                }
            }
            
        } catch (e: Exception) {
            logger.warn("KpiHelper: 查找插件 $pluginId 的Action时发生错误", e)
        }
        
        return actions
    }
    
    private fun isValidAIAction(actionId: String, action: Any): Boolean {
        try {
            // 检查Action的类名和包名
            val actionClass = action.javaClass
            val className = actionClass.name.lowercase()
            val packageName = actionClass.packageName?.lowercase() ?: ""
            
            // 排除一些明显不是AI交互的Action
            val excludePatterns = listOf(
                "settings", "config", "preference", "install", "update",
                "disable", "enable", "uninstall", "about", "help"
            )
            
            val actionIdLower = actionId.lowercase()
            val isExcluded = excludePatterns.any { pattern ->
                actionIdLower.contains(pattern)
            }
            
            if (isExcluded) return false
            
            // 优先选择包含交互关键词的Action
            val interactionKeywords = listOf(
                "chat", "ask", "query", "question", "explain", "generate",
                "complete", "suggest", "open", "show", "start"
            )
            
            val hasInteractionKeyword = interactionKeywords.any { keyword ->
                actionIdLower.contains(keyword) || className.contains(keyword)
            }
            
            return hasInteractionKeyword
            
        } catch (e: Exception) {
            logger.debug("KpiHelper: 验证Action $actionId 时发生错误", e)
            return false
        }
    }
    
    fun getPreferredAIActions(): List<String> {
        // 返回按优先级排序的AI Action列表
        val detectedActions = detectAIPlugins()
        
        // 定义优先级顺序
        val priorityPatterns = listOf(
            "chat", "ask", "open", "show", "start", "generate", "explain"
        )
        
        return detectedActions.sortedBy { actionId ->
            val actionIdLower = actionId.lowercase()
            priorityPatterns.indexOfFirst { pattern ->
                actionIdLower.contains(pattern)
            }.let { if (it == -1) Int.MAX_VALUE else it }
        }
    }
}
