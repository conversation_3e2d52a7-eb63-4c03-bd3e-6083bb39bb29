package cn.leo.helper

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.impl.SimpleDataContext
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManagerListener
import com.intellij.openapi.diagnostic.Logger
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import kotlin.random.Random

class PluginStartupListener : ProjectManagerListener {

    private var scheduler: ScheduledExecutorService? = null
    private var currentProject: Project? = null
    private val logger = Logger.getInstance(PluginStartupListener::class.java)
    private val aiPluginDetector = AIPluginDetector()
    private val questionGenerator = AIQuestionGenerator()
    private val config = KpiHelperConfig.getInstance()

    override fun projectOpened(project: Project) {
        if (scheduler != null && !scheduler!!.isShutdown) return

        currentProject = project

        // 检查是否启用
        if (!config.isEnabled()) {
            logger.info("KpiHelper: 插件已禁用，跳过启动")
            return
        }

        scheduler = Executors.newSingleThreadScheduledExecutor()

        // 首次执行
        scheduleNextExecution(config.initialDelayMinutes.toLong())

        logger.info("KpiHelper: AI助手自动化插件已启动，项目: ${project.name}")
    }

    override fun projectClosed(project: Project) {
        scheduler?.shutdownNow()
        scheduler = null
        currentProject = null
        logger.info("KpiHelper: AI助手自动化插件已停止")
    }

    private fun scheduleNextExecution(delayMinutes: Long) {
        val task = Runnable {
            try {
                // 检查是否仍然启用
                if (!config.isEnabled()) {
                    logger.info("KpiHelper: 插件已禁用，停止执行")
                    return@Runnable
                }

                // 检查是否在工作时间
                if (!config.isInWorkingHours()) {
                    logger.info("KpiHelper: 当前不在工作时间，跳过执行")
                    // 安排下次检查（较短间隔）
                    scheduleNextExecution(30) // 30分钟后再检查
                    return@Runnable
                }

                executeAIInteraction()
            } catch (e: Exception) {
                logger.error("KpiHelper: 执行AI交互时发生错误", e)
                config.incrementFailureCount()
            } finally {
                // 安排下次执行，使用随机间隔
                val nextDelay = config.getRandomInterval()
                scheduleNextExecution(nextDelay)
            }
        }

        scheduler?.schedule(task, delayMinutes, TimeUnit.MINUTES)
        logger.info("KpiHelper: 已安排下次AI交互，将在 $delayMinutes 分钟后执行")
    }

    private fun executeAIInteraction() {
        val project = currentProject ?: return

        logger.info("KpiHelper: 开始执行AI交互...")
        config.incrementInteractionCount()

        // 优先使用配置中的首选AI插件
        var aiActionIds = config.preferredAIPlugins.toList()

        // 如果没有首选插件或启用了自动检测，则检测AI插件
        if (aiActionIds.isEmpty() || config.autoDetectAIPlugins) {
            val detectedActions = aiPluginDetector.detectAIPlugins()
            aiActionIds = if (config.preferredAIPlugins.isNotEmpty()) {
                // 合并首选和检测到的插件
                (config.preferredAIPlugins + detectedActions).distinct()
            } else {
                detectedActions
            }
        }

        // 添加自定义Action ID
        aiActionIds = (aiActionIds + config.customAIActionIds).distinct()

        if (aiActionIds.isEmpty()) {
            logger.warn("KpiHelper: 未检测到任何AI插件，尝试使用预设的Action ID")
            tryPredefinedActions()
            return
        }

        // 随机选择一个AI插件
        val selectedActionId = aiActionIds.random()
        logger.info("KpiHelper: 选择AI插件: $selectedActionId")

        // 生成问题
        val question = if (config.customQuestions.isNotEmpty() && Random.nextBoolean()) {
            // 50%概率使用自定义问题
            config.customQuestions.random()
        } else {
            questionGenerator.generateQuestion()
        }
        logger.info("KpiHelper: 生成问题: $question")

        // 执行AI动作
        executeAIAction(selectedActionId, project, question)
    }

    private fun executeAIAction(actionId: String, project: Project, question: String, retryCount: Int = 0) {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction(actionId)

        if (action == null) {
            logger.warn("KpiHelper: Action '$actionId' 未找到")
            if (retryCount < config.maxRetries) {
                // 尝试其他AI插件
                val alternativeActions = aiPluginDetector.detectAIPlugins().filter { it != actionId }
                if (alternativeActions.isNotEmpty()) {
                    executeAIAction(alternativeActions.random(), project, question, retryCount + 1)
                }
            }
            return
        }

        ApplicationManager.getApplication().invokeLater {
            try {
                val dataContext = SimpleDataContext.getProjectContext(project)
                val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.AutoInteraction", dataContext)

                action.actionPerformed(event)
                logger.info("KpiHelper: 成功触发AI插件动作: $actionId")
                config.incrementSuccessCount()

                // 模拟用户输入问题（如果启用）
                if (config.simulateUserInput) {
                    simulateUserInput(question)
                }

            } catch (e: Exception) {
                logger.error("KpiHelper: 执行AI动作时发生错误: $actionId", e)
                config.incrementFailureCount()

                if (retryCount < config.maxRetries) {
                    // 延迟重试
                    scheduler?.schedule({
                        executeAIAction(actionId, project, question, retryCount + 1)
                    }, 30, TimeUnit.SECONDS)
                }
            }
        }
    }

    private fun simulateUserInput(question: String) {
        // 这里可以尝试模拟键盘输入，但需要根据具体的AI插件来实现
        // 大多数AI插件会打开一个对话框或面板，我们可以尝试向其发送文本
        ApplicationManager.getApplication().executeOnPooledThread {
            try {
                // 使用配置的延迟时间，让AI插件界面完全加载
                Thread.sleep(config.inputDelaySeconds * 1000L)

                // 这里可以添加具体的文本输入逻辑
                // 例如使用Robot类或者直接操作组件
                logger.info("KpiHelper: 模拟用户输入: $question")

                // 可以尝试使用系统剪贴板和键盘事件
                // 但这需要根据具体的AI插件界面来实现

            } catch (e: Exception) {
                logger.warn("KpiHelper: 模拟用户输入失败", e)
            }
        }
    }

    private fun tryPredefinedActions() {
        // 尝试一些可能的YTO AI插件Action ID
        val possibleYTOActionIds = listOf(
            // YtoAICode Tool Window相关的Action ID
            "YtoAICode",
            "ActivateYtoAICodeToolWindow",
            "YtoAICode.ToolWindow",
            "YtoAICode.Show",
            "YtoAICode.Open",

            // 可能的YTO AI插件Action ID模式
            "yto.ai.chat",
            "yto.ai.assistant",
            "yto.aicode.chat",
            "ytoai.action.chat",
            "ytoai.action.ask",
            "ytoai.action.generate",
            "ai.chat.action",
            "ai.assistant.action",
            "chat.action",
            "ask.action",
            "generate.action",
            "explain.action"
        )

        val project = currentProject ?: return
        val question = questionGenerator.generateQuestion()

        logger.info("KpiHelper: 尝试预设的YTO AI插件Action ID")

        for (actionId in possibleYTOActionIds) {
            val actionManager = ActionManager.getInstance()
            val action = actionManager.getAction(actionId)
            if (action != null) {
                logger.info("KpiHelper: 找到YTO AI插件: $actionId")
                executeAIAction(actionId, project, question)
                return
            }
        }

        // 如果都没找到，尝试列出所有可用的Action来帮助调试
        listAllAvailableActions()
        logger.warn("KpiHelper: 未找到任何可用的YTO AI插件Action")
    }

    private fun listAllAvailableActions() {
        try {
            val actionManager = ActionManager.getInstance()
            val allActionIds = actionManager.getActionIdList("")

            logger.info("KpiHelper: 开始列出所有可用的Action (总共${allActionIds.size}个)")

            // 只列出可能相关的Action
            val relevantActions = allActionIds.filter { actionId ->
                val actionIdLower = actionId.lowercase()
                actionIdLower.contains("ai") ||
                actionIdLower.contains("chat") ||
                actionIdLower.contains("assistant") ||
                actionIdLower.contains("yto") ||
                actionIdLower.contains("ask") ||
                actionIdLower.contains("generate") ||
                actionIdLower.contains("explain")
            }

            logger.info("KpiHelper: 找到 ${relevantActions.size} 个可能相关的Action:")
            relevantActions.forEach { actionId ->
                logger.info("KpiHelper: 可用Action: $actionId")
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 列出Action时发生错误", e)
        }
    }
}
