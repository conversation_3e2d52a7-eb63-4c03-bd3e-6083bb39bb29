package cn.leo.helper

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.impl.SimpleDataContext
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManagerListener
import com.intellij.openapi.diagnostic.Logger
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import kotlin.random.Random

class PluginStartupListener : ProjectManagerListener {

    private var scheduler: ScheduledExecutorService? = null
    private var currentProject: Project? = null
    private val logger = Logger.getInstance(PluginStartupListener::class.java)
    private val aiPluginDetector = AIPluginDetector()
    private val questionGenerator = AIQuestionGenerator()

    // 配置参数
    private val minIntervalMinutes = 60  // 最小间隔1小时
    private val maxIntervalMinutes = 180 // 最大间隔3小时
    private val initialDelayMinutes = 5  // 初始延迟5分钟
    private val maxRetries = 3           // 最大重试次数

    override fun projectOpened(project: Project) {
        if (scheduler != null && !scheduler!!.isShutdown) return

        currentProject = project
        scheduler = Executors.newSingleThreadScheduledExecutor()

        // 首次执行
        scheduleNextExecution(initialDelayMinutes.toLong())

        logger.info("KpiHelper: AI助手自动化插件已启动，项目: ${project.name}")
    }

    override fun projectClosed(project: Project) {
        scheduler?.shutdownNow()
        scheduler = null
        currentProject = null
        logger.info("KpiHelper: AI助手自动化插件已停止")
    }

    private fun scheduleNextExecution(delayMinutes: Long) {
        val task = Runnable {
            try {
                executeAIInteraction()
            } catch (e: Exception) {
                logger.error("KpiHelper: 执行AI交互时发生错误", e)
            } finally {
                // 安排下次执行，使用随机间隔
                val nextDelay = Random.nextLong(minIntervalMinutes.toLong(), maxIntervalMinutes.toLong())
                scheduleNextExecution(nextDelay)
            }
        }

        scheduler?.schedule(task, delayMinutes, TimeUnit.MINUTES)
        logger.info("KpiHelper: 已安排下次AI交互，将在 $delayMinutes 分钟后执行")
    }

    private fun executeAIInteraction() {
        val project = currentProject ?: return

        logger.info("KpiHelper: 开始执行AI交互...")

        // 检测AI插件
        val aiActionIds = aiPluginDetector.detectAIPlugins()
        if (aiActionIds.isEmpty()) {
            logger.warn("KpiHelper: 未检测到任何AI插件，尝试使用预设的Action ID")
            tryPredefinedActions()
            return
        }

        // 随机选择一个AI插件
        val selectedActionId = aiActionIds.random()
        logger.info("KpiHelper: 选择AI插件: $selectedActionId")

        // 生成问题并执行交互
        val question = questionGenerator.generateQuestion()
        logger.info("KpiHelper: 生成问题: $question")

        // 执行AI动作
        executeAIAction(selectedActionId, project, question)
    }

    private fun executeAIAction(actionId: String, project: Project, question: String, retryCount: Int = 0) {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction(actionId)

        if (action == null) {
            logger.warn("KpiHelper: Action '$actionId' 未找到")
            if (retryCount < maxRetries) {
                // 尝试其他AI插件
                val alternativeActions = aiPluginDetector.detectAIPlugins().filter { it != actionId }
                if (alternativeActions.isNotEmpty()) {
                    executeAIAction(alternativeActions.random(), project, question, retryCount + 1)
                }
            }
            return
        }

        ApplicationManager.getApplication().invokeLater {
            try {
                val dataContext = SimpleDataContext.getProjectContext(project)
                val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.AutoInteraction", dataContext)

                action.actionPerformed(event)
                logger.info("KpiHelper: 成功触发AI插件动作: $actionId")

                // 模拟用户输入问题（如果插件支持）
                simulateUserInput(question)

            } catch (e: Exception) {
                logger.error("KpiHelper: 执行AI动作时发生错误: $actionId", e)
                if (retryCount < maxRetries) {
                    // 延迟重试
                    scheduler?.schedule({
                        executeAIAction(actionId, project, question, retryCount + 1)
                    }, 30, TimeUnit.SECONDS)
                }
            }
        }
    }

    private fun simulateUserInput(question: String) {
        // 这里可以尝试模拟键盘输入，但需要根据具体的AI插件来实现
        // 大多数AI插件会打开一个对话框或面板，我们可以尝试向其发送文本
        ApplicationManager.getApplication().invokeLater {
            try {
                // 模拟延迟，让AI插件界面完全加载
                Thread.sleep(2000)

                // 这里可以添加具体的文本输入逻辑
                // 例如使用Robot类或者直接操作组件
                logger.info("KpiHelper: 模拟用户输入: $question")

            } catch (e: Exception) {
                logger.warn("KpiHelper: 模拟用户输入失败", e)
            }
        }
    }

    private fun tryPredefinedActions() {
        // 尝试一些常见的AI插件Action ID
        val commonAIActionIds = listOf(
            "com.github.copilot.action.OpenCopilotChat",
            "com.tabnine.action.OpenTabnineChat",
            "com.codeium.action.OpenCodeiumChat",
            "ai.assistant.action.OpenChat",
            "codegpt.action.OpenChat",
            "YOUR_AI_PLUGIN_ACTION_ID_HERE" // 用户可以手动替换
        )

        val project = currentProject ?: return
        val question = questionGenerator.generateQuestion()

        for (actionId in commonAIActionIds) {
            val actionManager = ActionManager.getInstance()
            val action = actionManager.getAction(actionId)
            if (action != null) {
                logger.info("KpiHelper: 找到预设AI插件: $actionId")
                executeAIAction(actionId, project, question)
                return
            }
        }

        logger.warn("KpiHelper: 未找到任何可用的AI插件")
    }
}
