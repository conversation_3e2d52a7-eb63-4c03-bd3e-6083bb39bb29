package cn.leo.helper

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.impl.SimpleDataContext
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManagerListener
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

class PluginStartupListener : ProjectManagerListener {

    private var scheduler: ScheduledExecutorService? = null

    override fun projectOpened(project: Project) {
        if (scheduler != null && !scheduler!!.isShutdown) return

        scheduler = Executors.newSingleThreadScheduledExecutor()

        val task = Runnable {
            println("KpiHelper: Triggering AI plugin action...")
            try {
                triggerAiAction()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        val initialDelay: Long = 5 // 首次延迟5分钟
        val period: Long = 120    // 每隔120分钟（2小时）执行一次

        scheduler?.scheduleAtFixedRate(task, initialDelay, period, TimeUnit.MINUTES)
        println("KpiHelper: Scheduler started. Will run every $period minutes.")
    }

    override fun projectClosed(project: Project) {
        scheduler?.shutdownNow()
        scheduler = null
        println("KpiHelper: Scheduler stopped.")
    }

    private fun triggerAiAction() {
        // **********************************************************************
        // ** 在这里换成你用 UI Inspector 找到的真实 Action ID **
        // **********************************************************************
        val actionId = "YOUR_AI_PLUGIN_ACTION_ID_HERE"

        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction(actionId)

        if (action == null) {
            println("KpiHelper Error: Action with ID '$actionId' not found. Make sure the ID is correct and the target plugin is enabled.")
            return
        }

        ApplicationManager.getApplication().invokeLater {
            val dataContext = SimpleDataContext.getProjectContext(null)
            val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.Plugin", dataContext)

            action.actionPerformed(event)
            println("KpiHelper: Action '$actionId' performed successfully.")
        }
    }
}
