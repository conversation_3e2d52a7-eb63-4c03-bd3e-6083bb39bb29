package cn.leo.helper.ui

import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.*
import com.intellij.ui.layout.panel
import cn.leo.helper.KpiHelperConfig
import cn.leo.helper.YTOAIPluginHelper
import java.awt.Dimension
import javax.swing.*

class KpiHelperConfigDialog(private val project: Project) : DialogWrapper(project) {
    
    private val config = KpiHelperConfig.getInstance()
    private val ytoHelper = YTOAIPluginHelper()
    
    // 基本设置组件
    private val enabledCheckBox = JBCheckBox("启用自动化", config.enabled)
    private val minIntervalSpinner = JSpinner(SpinnerNumberModel(config.minIntervalMinutes, 1, 1440, 1))
    private val maxIntervalSpinner = JSpinner(SpinnerNumberModel(config.maxIntervalMinutes, 1, 1440, 1))
    private val initialDelaySpinner = JSpinner(SpinnerNumberModel(config.initialDelayMinutes, 0, 60, 1))
    private val maxRetriesSpinner = JSpinner(SpinnerNumberModel(config.maxRetries, 0, 10, 1))
    
    // 时间限制组件
    private val workingHoursCheckBox = JBCheckBox("仅工作时间执行", config.enableWorkingHoursOnly)
    private val workingHoursStartSpinner = JSpinner(SpinnerNumberModel(config.workingHoursStart, 0, 23, 1))
    private val workingHoursEndSpinner = JSpinner(SpinnerNumberModel(config.workingHoursEnd, 0, 23, 1))
    private val weekdaysOnlyCheckBox = JBCheckBox("仅工作日执行", config.enableWeekdaysOnly)
    
    // AI插件设置组件
    private val autoDetectCheckBox = JBCheckBox("自动检测AI插件", config.autoDetectAIPlugins)
    private val customActionIdField = JBTextField()
    private val customActionIdsList = JBList<String>()
    private val customActionIdsModel = DefaultListModel<String>()
    
    // 问题生成组件
    private val randomQuestionsCheckBox = JBCheckBox("启用随机问题", config.enableRandomQuestions)
    private val customQuestionField = JBTextField()
    private val customQuestionsList = JBList<String>()
    private val customQuestionsModel = DefaultListModel<String>()
    
    // 执行设置组件
    private val simulateInputCheckBox = JBCheckBox("模拟用户输入", config.simulateUserInput)
    private val inputDelaySpinner = JSpinner(SpinnerNumberModel(config.inputDelaySeconds, 0, 10, 1))
    private val enableLoggingCheckBox = JBCheckBox("启用日志", config.enableLogging)
    
    // 状态显示组件
    private val statusLabel = JBLabel()
    private val statisticsLabel = JBLabel()
    
    init {
        title = "YTO AI助手自动化配置"
        setSize(600, 700)
        init()
        loadCurrentConfig()
        updateStatusDisplay()
    }
    
    override fun createCenterPanel(): JComponent {
        return panel {
            // 基本设置
            titledRow("基本设置") {
                row { enabledCheckBox() }
                row("执行间隔 (分钟):") {
                    label("最小:")
                    minIntervalSpinner(growX)
                    label("最大:")
                    maxIntervalSpinner(growX)
                }
                row("初始延迟 (分钟):") { initialDelaySpinner(growX) }
                row("最大重试次数:") { maxRetriesSpinner(growX) }
            }
            
            // 时间限制设置
            titledRow("时间限制") {
                row { workingHoursCheckBox() }
                row("工作时间:") {
                    workingHoursStartSpinner(growX)
                    label("到")
                    workingHoursEndSpinner(growX)
                    label("点")
                }
                row { weekdaysOnlyCheckBox() }
            }
            
            // AI插件设置
            titledRow("YTO AI插件设置") {
                row { autoDetectCheckBox() }
                row {
                    button("查找YTO AI插件") {
                        findYTOAIPlugin()
                    }
                    button("测试当前配置") {
                        testCurrentConfig()
                    }
                }
                row("自定义Action ID:") {
                    customActionIdField(growX)
                    button("添加") {
                        addCustomActionId()
                    }
                }
                row {
                    scrollPane(customActionIdsList).apply {
                        preferredSize = Dimension(400, 100)
                    }
                    cell {
                        button("删除选中") {
                            removeSelectedActionId()
                        }
                    }
                }
            }
            
            // 问题生成设置
            titledRow("问题生成") {
                row { randomQuestionsCheckBox() }
                row("自定义问题:") {
                    customQuestionField(growX)
                    button("添加") {
                        addCustomQuestion()
                    }
                }
                row {
                    scrollPane(customQuestionsList).apply {
                        preferredSize = Dimension(400, 100)
                    }
                    cell {
                        button("删除选中") {
                            removeSelectedQuestion()
                        }
                    }
                }
            }
            
            // 执行设置
            titledRow("执行设置") {
                row { simulateInputCheckBox() }
                row("输入延迟 (秒):") { inputDelaySpinner(growX) }
                row { enableLoggingCheckBox() }
            }
            
            // 状态显示
            titledRow("当前状态") {
                row { statusLabel() }
                row { statisticsLabel() }
                row {
                    button("刷新状态") {
                        updateStatusDisplay()
                    }
                    button("重置统计") {
                        resetStatistics()
                    }
                }
            }
        }
    }
    
    private fun loadCurrentConfig() {
        // 加载自定义Action ID列表
        customActionIdsModel.clear()
        config.customAIActionIds.forEach { customActionIdsModel.addElement(it) }
        config.preferredAIPlugins.forEach { customActionIdsModel.addElement(it) }
        customActionIdsList.model = customActionIdsModel
        
        // 加载自定义问题列表
        customQuestionsModel.clear()
        config.customQuestions.forEach { customQuestionsModel.addElement(it) }
        customQuestionsList.model = customQuestionsModel
    }
    
    private fun findYTOAIPlugin() {
        val foundActionId = ytoHelper.findYTOAIActionId()
        
        if (foundActionId != null) {
            customActionIdField.text = foundActionId
            JOptionPane.showMessageDialog(
                contentPanel,
                "找到YTO AI插件Action ID:\n$foundActionId\n\n点击'添加'按钮将其加入配置。",
                "找到插件",
                JOptionPane.INFORMATION_MESSAGE
            )
        } else {
            val allActions = ytoHelper.listAllPossibleActions()
            val message = if (allActions.isNotEmpty()) {
                "未自动找到YTO AI插件，但发现以下可能相关的Action:\n\n" +
                allActions.take(10).joinToString("\n") +
                if (allActions.size > 10) "\n... 还有 ${allActions.size - 10} 个" else ""
            } else {
                "未找到任何相关的Action。请确保YTO AI插件已正确安装并启用。"
            }
            
            JOptionPane.showMessageDialog(
                contentPanel,
                message,
                "查找结果",
                JOptionPane.WARNING_MESSAGE
            )
        }
    }
    
    private fun testCurrentConfig() {
        val actionIds = mutableListOf<String>()
        
        // 收集所有配置的Action ID
        for (i in 0 until customActionIdsModel.size()) {
            actionIds.add(customActionIdsModel.getElementAt(i))
        }
        
        if (actionIds.isEmpty()) {
            JOptionPane.showMessageDialog(
                contentPanel,
                "请先添加至少一个Action ID",
                "配置为空",
                JOptionPane.WARNING_MESSAGE
            )
            return
        }
        
        val results = mutableListOf<String>()
        for (actionId in actionIds) {
            val isValid = ytoHelper.validateActionId(actionId)
            results.add("$actionId: ${if (isValid) "✓ 有效" else "✗ 无效"}")
        }
        
        JOptionPane.showMessageDialog(
            contentPanel,
            "Action ID验证结果:\n\n" + results.joinToString("\n"),
            "测试结果",
            JOptionPane.INFORMATION_MESSAGE
        )
    }
    
    private fun addCustomActionId() {
        val actionId = customActionIdField.text.trim()
        if (actionId.isNotEmpty() && !customActionIdsModel.contains(actionId)) {
            customActionIdsModel.addElement(actionId)
            customActionIdField.text = ""
        }
    }
    
    private fun removeSelectedActionId() {
        val selectedIndex = customActionIdsList.selectedIndex
        if (selectedIndex >= 0) {
            customActionIdsModel.removeElementAt(selectedIndex)
        }
    }
    
    private fun addCustomQuestion() {
        val question = customQuestionField.text.trim()
        if (question.isNotEmpty() && !customQuestionsModel.contains(question)) {
            customQuestionsModel.addElement(question)
            customQuestionField.text = ""
        }
    }
    
    private fun removeSelectedQuestion() {
        val selectedIndex = customQuestionsList.selectedIndex
        if (selectedIndex >= 0) {
            customQuestionsModel.removeElementAt(selectedIndex)
        }
    }
    
    private fun updateStatusDisplay() {
        val status = if (config.enabled) "已启用" else "已禁用"
        val workingHours = if (config.isInWorkingHours()) "是" else "否"
        statusLabel.text = "功能状态: $status | 当前在工作时间: $workingHours"
        
        val successRate = String.format("%.1f%%", config.getSuccessRate() * 100)
        statisticsLabel.text = "总执行: ${config.totalInteractions} | 成功: ${config.successfulInteractions} | 成功率: $successRate"
    }
    
    private fun resetStatistics() {
        val result = JOptionPane.showConfirmDialog(
            contentPanel,
            "确定要重置所有统计信息吗？",
            "确认重置",
            JOptionPane.YES_NO_OPTION
        )
        
        if (result == JOptionPane.YES_OPTION) {
            config.resetStatistics()
            updateStatusDisplay()
        }
    }
    
    override fun doOKAction() {
        // 保存配置
        saveConfig()
        super.doOKAction()
    }
    
    private fun saveConfig() {
        // 基本设置
        config.enabled = enabledCheckBox.isSelected
        config.minIntervalMinutes = minIntervalSpinner.value as Int
        config.maxIntervalMinutes = maxIntervalSpinner.value as Int
        config.initialDelayMinutes = initialDelaySpinner.value as Int
        config.maxRetries = maxRetriesSpinner.value as Int
        
        // 时间限制
        config.enableWorkingHoursOnly = workingHoursCheckBox.isSelected
        config.workingHoursStart = workingHoursStartSpinner.value as Int
        config.workingHoursEnd = workingHoursEndSpinner.value as Int
        config.enableWeekdaysOnly = weekdaysOnlyCheckBox.isSelected
        
        // AI插件设置
        config.autoDetectAIPlugins = autoDetectCheckBox.isSelected
        config.customAIActionIds.clear()
        config.preferredAIPlugins.clear()
        for (i in 0 until customActionIdsModel.size()) {
            val actionId = customActionIdsModel.getElementAt(i)
            config.customAIActionIds.add(actionId)
            config.preferredAIPlugins.add(actionId)
        }
        
        // 问题生成
        config.enableRandomQuestions = randomQuestionsCheckBox.isSelected
        config.customQuestions.clear()
        for (i in 0 until customQuestionsModel.size()) {
            config.customQuestions.add(customQuestionsModel.getElementAt(i))
        }
        
        // 执行设置
        config.simulateUserInput = simulateInputCheckBox.isSelected
        config.inputDelaySeconds = inputDelaySpinner.value as Int
        config.enableLogging = enableLoggingCheckBox.isSelected
    }
}
