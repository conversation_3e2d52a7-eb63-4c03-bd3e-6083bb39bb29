<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>cn.leo.helper.YTOAIKpiHelper</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>YTOAIKpiHelper</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor>Leo</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
    仅供测试学习.<br>
  ]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>

    <applicationListeners>
        <listener class="cn.leo.helper.PluginStartupListener"
                  topic="com.intellij.openapi.project.ProjectManagerListener"/>
    </applicationListeners>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- 注册配置服务 -->
        <applicationService serviceImplementation="cn.leo.helper.KpiHelperConfig"/>
    </extensions>

    <!-- Actions -->
    <actions>
        <group id="KpiHelper.Menu" text="AI助手自动化" description="AI助手自动化工具">
            <add-to-group group-id="ToolsMenu" anchor="last"/>

            <action id="KpiHelper.ToggleAction"
                    class="cn.leo.helper.actions.ToggleKpiHelperAction"
                    text="启用/禁用自动化"
                    description="启用或禁用AI助手自动化功能"/>

            <action id="KpiHelper.TriggerNowAction"
                    class="cn.leo.helper.actions.TriggerNowAction"
                    text="立即触发一次"
                    description="立即执行一次AI交互"/>

            <action id="KpiHelper.ConfigAction"
                    class="cn.leo.helper.actions.OpenConfigAction"
                    text="配置设置"
                    description="打开配置设置对话框"/>

            <action id="KpiHelper.StatusAction"
                    class="cn.leo.helper.actions.ShowStatusAction"
                    text="查看状态"
                    description="查看自动化运行状态和统计信息"/>

            <action id="KpiHelper.FindYTOAIAction"
                    class="cn.leo.helper.actions.FindYTOAIActionAction"
                    text="查找YTO AI插件"
                    description="自动查找YTO AI插件的Action ID"/>
        </group>
    </actions>
</idea-plugin>
