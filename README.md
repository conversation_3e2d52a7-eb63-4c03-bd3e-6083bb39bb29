# YTO AI KPI Helper

一个IntelliJ IDEA插件，专门用于自动化与**公司YTO AI编程助手**的交互，帮助满足公司对AI工具使用的KPI要求。

## 功能特性

### 🤖 自动YTO AI交互
- 自动检测公司的YTO AI编程助手插件
- 定时自动触发YTO AI插件，模拟真实的使用场景
- 智能生成编程相关问题，涵盖代码审查、架构设计、调试等多个方面

### ⏰ 灵活的时间调度
- 可配置的执行间隔（默认1-3小时随机间隔）
- 支持工作时间限制（仅在指定时间段内执行）
- 支持工作日限制（仅在工作日执行）
- 随机化执行时间，避免被检测为自动化行为

### 🎯 智能问题生成
- 内置200+个编程相关问题模板
- 支持多种编程语言（Kotlin、Java、JavaScript、Python等）
- 支持自定义问题列表
- 根据当前文件类型生成相关问题

### 📊 统计和监控
- 详细的执行统计信息
- 成功率监控
- 执行历史记录
- 实时状态查看

### 🛠️ 易于配置
- 图形化配置界面
- 支持导入/导出配置
- 实时配置验证
- 一键重置为默认设置

## 安装和使用

### 1. 构建和安装插件

1. 在IntelliJ IDEA中打开项目
2. 运行`./gradlew buildPlugin`构建插件
3. 安装生成的插件文件，或运行`./gradlew runIde`进行测试

### 2. 确保YTO AI插件已安装

确保您公司的YTO AI编程助手插件已经正确安装：
- YTO AI插件应该已经从`libs/YtoAICode.zip`加载
- 插件应该处于启用状态
- 可以通过插件管理器确认插件状态

### 3. 启动自动化

插件会在项目打开时自动启动。您也可以通过菜单控制：

**Tools → AI助手自动化**
- **启用/禁用自动化** - 开启或关闭自动化功能
- **立即触发一次** - 手动执行一次YTO AI交互
- **配置设置** - 打开配置对话框
- **查看状态** - 查看当前运行状态和统计信息
- **查找YTO AI插件** - 自动查找YTO AI插件的Action ID

## 配置选项

### 基本设置
- **启用状态**: 开启/关闭自动化功能
- **执行间隔**: 设置最小和最大执行间隔（分钟）
- **初始延迟**: 插件启动后的初始等待时间
- **最大重试次数**: 执行失败时的重试次数

### 时间限制
- **工作时间限制**: 仅在指定时间段内执行
- **工作日限制**: 仅在工作日执行
- **自定义工作时间**: 设置具体的工作时间段

### 问题生成
- **随机问题**: 启用内置问题模板
- **问题类型**: 选择要使用的问题类别
- **自定义问题**: 添加您自己的问题列表

### AI插件设置
- **自动检测**: 自动发现AI插件
- **首选插件**: 设置优先使用的AI插件
- **自定义Action ID**: 手动指定AI插件的Action ID

## 工作原理

1. **插件检测**: 自动扫描已安装的AI编程助手插件
2. **问题生成**: 根据配置生成相关的编程问题
3. **动作触发**: 调用AI插件的Action来打开对话界面
4. **模拟交互**: 可选择模拟用户输入问题
5. **统计记录**: 记录执行结果和统计信息

## 支持的AI插件

插件会自动检测以下AI编程助手：

- **GitHub Copilot** (`com.github.copilot.*`)
- **Tabnine** (`com.tabnine.*`)
- **Codeium** (`com.codeium.*`)
- **CodeGPT** (`codegpt.*`, `ee.carlrobert.codegpt.*`)
- **JetBrains AI Assistant** (`com.intellij.ml.llm.*`)
- **其他AI助手** (包含`ai`、`chat`、`assistant`等关键词的插件)

## 问题类别

内置问题涵盖以下类别：

- **编程通用**: 代码优化、性能改进、bug修复
- **语言特定**: Kotlin、Java、JavaScript等语言的最佳实践
- **架构设计**: 设计模式、系统架构、可扩展性
- **代码审查**: 代码质量、规范性、安全性
- **调试排错**: 问题诊断、性能分析、错误处理
- **学习提升**: 技术概念、最佳实践、工具使用

## 自定义配置

### 添加自定义问题

```kotlin
// 通过配置界面或直接修改配置
config.addCustomQuestion("请帮我优化这段代码的性能")
config.addCustomQuestion("这个算法的时间复杂度是多少？")
```

### 指定AI插件Action ID

如果自动检测失败，您可以手动指定AI插件的Action ID：

1. 使用IntelliJ IDEA的UI Inspector工具查找Action ID
2. 在配置中添加自定义Action ID
3. 或者直接修改代码中的`YOUR_AI_PLUGIN_ACTION_ID_HERE`

### 配置文件位置

配置文件保存在：`~/.config/JetBrains/IntelliJIdea{version}/options/kpi-helper-config.xml`

## 注意事项

1. **合规使用**: 请确保您的使用符合公司政策和AI工具的使用条款
2. **资源消耗**: 插件会定期执行，可能会消耗一定的系统资源
3. **网络请求**: AI插件可能会发起网络请求，请注意网络使用情况
4. **隐私保护**: 生成的问题都是通用的编程问题，不包含敏感信息

## 故障排除

### 常见问题

1. **未检测到AI插件**
   - 确保AI插件已正确安装并启用
   - 检查插件是否在支持的列表中
   - 尝试手动指定Action ID

2. **执行失败**
   - 查看IDE日志获取详细错误信息
   - 检查AI插件是否正常工作
   - 尝试手动触发AI插件

3. **配置不生效**
   - 重启IDE
   - 检查配置文件是否正确保存
   - 验证配置参数的有效性

### 日志查看

插件日志会输出到IDE的日志文件中，可以通过以下方式查看：
- **Help → Show Log in Finder/Explorer**
- 搜索关键词：`KpiHelper`

## 开发和贡献

### 项目结构

```
src/main/kotlin/cn/leo/helper/
├── PluginStartupListener.kt      # 主要的启动和调度逻辑
├── AIPluginDetector.kt           # AI插件检测
├── AIQuestionGenerator.kt        # 问题生成器
├── KpiHelperConfig.kt           # 配置管理
└── actions/                     # 用户界面Action
    ├── ToggleKpiHelperAction.kt
    ├── TriggerNowAction.kt
    ├── OpenConfigAction.kt
    └── ShowStatusAction.kt
```

### 构建项目

```bash
# 编译项目
./gradlew build

# 运行带插件的IDE
./gradlew runIde

# 构建插件包
./gradlew buildPlugin
```

## 许可证

本项目仅供学习和测试使用。请遵守相关法律法规和公司政策。

## 更新日志

### v1.0.0
- 初始版本
- 支持自动检测AI插件
- 内置问题生成器
- 基本的配置和统计功能
- 图形化用户界面
