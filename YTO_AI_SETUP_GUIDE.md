# YTO AI插件自动化设置指南

本指南将帮助您设置自动化工具来定时使用公司的YTO AI编程助手插件。

## 快速设置步骤

### 1. 确认YTO AI插件已加载

首先确认YTO AI插件已经正确加载：

```bash
# 启动带有YTO AI插件的IDE
./gradlew runIde
```

启动后，您应该能在IDE中看到YTO AI插件的相关功能。

### 2. 查找YTO AI插件的Action ID

这是最关键的一步，需要找到YTO AI插件的正确Action ID：

#### 方法1：使用自动查找功能
1. 在IDE中打开任意项目
2. 点击菜单：**Tools → AI助手自动化 → 查找YTO AI插件**
3. 系统会自动扫描并尝试找到YTO AI插件的Action ID
4. 如果找到，会询问是否保存到配置中

#### 方法2：手动查找Action ID
如果自动查找失败，可以手动查找：

1. 使用IntelliJ IDEA的UI Inspector：
   ```
   Help → Diagnostic Tools → UI Inspector
   ```

2. 启动UI Inspector后，点击YTO AI插件的按钮或菜单项

3. 在Inspector中查看Action ID，通常格式类似：
   ```
   yto.ai.chat
   yto.aicode.action
   ai.assistant.action
   ```

4. 将找到的Action ID添加到配置中

#### 方法3：查看日志输出
1. 点击菜单：**Tools → AI助手自动化 → 查看状态**
2. 查看日志中列出的所有相关Action
3. 从中找到YTO相关的Action ID

### 3. 配置Action ID

找到正确的Action ID后，需要将其配置到系统中：

#### 通过界面配置：
1. 点击菜单：**Tools → AI助手自动化 → 配置设置**
2. 选择"查看详细配置"
3. 记下当前配置，然后手动编辑配置文件

#### 通过代码配置：
如果知道确切的Action ID，可以直接修改代码：

1. 编辑文件：`src/main/kotlin/cn/leo/helper/PluginStartupListener.kt`
2. 找到`tryPredefinedActions()`方法
3. 将`YOUR_YTO_AI_PLUGIN_ACTION_ID_HERE`替换为实际的Action ID

### 4. 测试配置

配置完成后，测试是否工作正常：

1. 点击菜单：**Tools → AI助手自动化 → 立即触发一次**
2. 观察是否成功打开YTO AI插件
3. 查看日志输出确认执行状态

## 常见的YTO AI插件Action ID模式

根据常见的插件命名规范，YTO AI插件的Action ID可能是：

```
# YTO特定的模式
yto.ai.chat
yto.ai.assistant
yto.aicode.chat
yto.aicode.action
ytoai.action.chat
ytoai.action.ask

# 通用AI模式
ai.chat.action
ai.assistant.action
chat.action
ask.action
generate.action
```

## 配置参数说明

### 基本设置
- **执行间隔**: 建议设置为60-180分钟，避免过于频繁
- **工作时间限制**: 可以设置仅在工作时间执行
- **问题类型**: 选择适合的问题类别

### 推荐配置
```
执行间隔: 90-150分钟（随机）
工作时间: 9:00-18:00
仅工作日: 是
问题类型: 编程通用、代码审查、架构设计
```

## 验证自动化是否工作

### 检查日志
查看IDE日志，应该看到类似信息：
```
KpiHelper: AI助手自动化插件已启动
KpiHelper: 找到YTO AI插件: yto.ai.chat
KpiHelper: 已安排下次AI交互，将在 120 分钟后执行
```

### 查看统计信息
通过菜单查看执行统计：
```
Tools → AI助手自动化 → 查看状态
```

应该显示：
- 检测到的YTO AI插件
- 执行次数和成功率
- 下次执行时间

## 故障排除

### 问题1：未找到YTO AI插件
**可能原因**：
- YTO AI插件未正确安装
- 插件被禁用
- Action ID不正确

**解决方案**：
1. 确认插件在插件管理器中显示为已启用
2. 重启IDE
3. 使用UI Inspector手动查找Action ID
4. 检查插件是否正常工作

### 问题2：执行失败
**可能原因**：
- Action ID错误
- YTO AI插件出现问题
- 网络连接问题

**解决方案**：
1. 验证Action ID是否正确
2. 手动测试YTO AI插件是否正常工作
3. 查看详细错误日志
4. 尝试不同的Action ID

### 问题3：自动化不执行
**可能原因**：
- 插件被禁用
- 不在工作时间
- 配置错误

**解决方案**：
1. 检查插件是否启用
2. 确认当前时间是否在配置的工作时间内
3. 验证配置参数
4. 重启IDE

## 高级配置

### 自定义问题
可以添加特定于您工作的问题：
```
请帮我检查这段业务逻辑代码
这个API设计是否合理？
如何优化这个数据处理流程？
```

### 多个Action ID
如果YTO AI插件有多个入口，可以配置多个Action ID：
```
yto.ai.chat          # 聊天功能
yto.ai.generate      # 代码生成
yto.ai.explain       # 代码解释
```

### 时间策略
根据使用要求调整时间策略：
```
# 满足基本KPI要求
间隔: 120-180分钟
每天: 3-4次

# 积极使用
间隔: 60-90分钟  
每天: 5-8次
```

## 注意事项

1. **合规使用**: 确保符合公司的AI工具使用政策
2. **适度频率**: 不要设置过高的执行频率
3. **监控效果**: 定期检查自动化是否正常工作
4. **保持更新**: 如果YTO AI插件更新，可能需要重新配置Action ID

## 支持和帮助

如果遇到问题：
1. 查看IDE日志获取详细错误信息
2. 使用"查看状态"功能检查当前配置
3. 尝试手动触发功能进行测试
4. 确认YTO AI插件本身是否正常工作

通过正确配置，这个自动化工具可以帮助您轻松满足公司对YTO AI工具使用的要求，同时不会干扰正常的开发工作。
