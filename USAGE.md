# 使用指南

## 快速开始

### 1. 确认AI插件已安装

在开始使用之前，请确保您已经安装了至少一个AI编程助手插件：

- **GitHub Copilot**: 最流行的AI编程助手
- **Tabnine**: 支持多种编程语言的AI补全
- **Codeium**: 免费的AI编程助手
- **CodeGPT**: 基于GPT的编程助手
- **JetBrains AI Assistant**: JetBrains官方AI助手

### 2. 启动项目

```bash
# 克隆或打开项目
cd /path/to/YTOAIKpiHelper

# 运行带插件的IDE
./gradlew runIde
```

### 3. 验证插件工作

1. 打开任意项目
2. 查看IDE日志，应该看到类似信息：
   ```
   KpiHelper: AI助手自动化插件已启动，项目: YourProject
   KpiHelper: 检测到 X 个AI相关Action
   KpiHelper: 已安排下次AI交互，将在 5 分钟后执行
   ```

## 基本操作

### 菜单操作

通过 **Tools → AI助手自动化** 菜单可以进行以下操作：

#### 启用/禁用自动化
- 快速开启或关闭自动化功能
- 状态会实时保存，重启IDE后保持

#### 立即触发一次
- 手动执行一次AI交互，用于测试
- 会显示执行结果和使用的问题

#### 查看状态
- 显示当前运行状态
- 查看检测到的AI插件列表
- 查看执行统计信息

#### 配置设置
- 打开配置对话框
- 可以重置为默认配置
- 查看详细配置信息

## 配置详解

### 基本配置

```
启用状态: true/false
执行间隔: 60-180分钟（随机）
初始延迟: 5分钟
最大重试: 3次
```

**建议设置**：
- 间隔时间不要太短，避免过于频繁
- 初始延迟给AI插件足够的加载时间
- 重试次数适中，避免无限重试

### 时间限制配置

```
工作时间限制: false（默认关闭）
工作时间: 9:00-18:00
仅工作日: false（默认关闭）
```

**使用场景**：
- 如果只想在工作时间使用，开启工作时间限制
- 如果不想在周末执行，开启仅工作日选项

### 问题生成配置

```
随机问题: true（推荐开启）
问题类型: 所有类型（默认）
自定义问题: 可添加特定问题
```

**自定义问题示例**：
```
请帮我检查这段代码的质量
这个方法有什么可以优化的地方？
如何改进这段代码的可读性？
这段代码遵循了最佳实践吗？
```

## 高级配置

### 手动指定AI插件Action ID

如果自动检测失败，可以手动指定：

1. **使用UI Inspector查找Action ID**：
   ```
   Help → Diagnostic Tools → UI Inspector
   ```
   
2. **常见AI插件Action ID**：
   ```
   GitHub Copilot: com.github.copilot.action.OpenCopilotChat
   Tabnine: com.tabnine.action.OpenTabnineChat
   Codeium: com.codeium.action.OpenCodeiumChat
   ```

3. **在配置中添加**：
   - 通过配置界面添加到"首选AI插件"
   - 或添加到"自定义Action ID"列表

### 自定义问题策略

根据您的需求，可以采用不同的问题策略：

#### 策略1：通用编程问题
```
启用所有问题类型
不添加自定义问题
让系统随机选择
```

#### 策略2：特定领域问题
```
只启用相关的问题类型（如KOTLIN、JAVA）
添加项目相关的自定义问题
```

#### 策略3：完全自定义
```
关闭随机问题
只使用自定义问题列表
```

## 监控和调试

### 查看执行状态

通过"查看状态"菜单可以看到：

```
=== AI助手自动化状态 ===

基本状态:
• 功能状态: 已启用
• 工作时间限制: 已禁用
• 当前是否在工作时间: 是

执行配置:
• 执行间隔: 60-180 分钟
• 初始延迟: 5 分钟
• 最大重试次数: 3

检测到的AI插件 (2个):
• com.github.copilot.action.OpenCopilotChat
• com.tabnine.action.OpenTabnineChat

统计信息:
• 总交互次数: 15
• 成功次数: 14
• 失败次数: 1
• 成功率: 93.3%
• 最后执行时间: 2024-01-15 14:30:25
```

### 查看日志

IDE日志中会记录详细的执行信息：

```
KpiHelper: 开始执行AI交互...
KpiHelper: 选择AI插件: com.github.copilot.action.OpenCopilotChat
KpiHelper: 生成问题: 如何优化这段代码的性能？
KpiHelper: 成功触发AI插件动作: com.github.copilot.action.OpenCopilotChat
KpiHelper: 模拟用户输入: 如何优化这段代码的性能？
KpiHelper: 已安排下次AI交互，将在 127 分钟后执行
```

### 故障排除

#### 问题1：未检测到AI插件
```
解决方案：
1. 确认AI插件已安装并启用
2. 重启IDE
3. 手动添加Action ID到配置中
4. 检查插件是否在支持列表中
```

#### 问题2：执行失败率高
```
解决方案：
1. 检查AI插件是否正常工作
2. 减少执行频率
3. 增加重试次数
4. 查看详细错误日志
```

#### 问题3：不在工作时间执行
```
解决方案：
1. 检查工作时间配置
2. 确认系统时间正确
3. 关闭工作时间限制进行测试
```

## 最佳实践

### 1. 合理设置执行频率
- 不要设置过于频繁的执行间隔
- 考虑AI服务的使用限制
- 避免在高峰时段过度使用

### 2. 多样化问题内容
- 使用多种类型的问题
- 定期更新自定义问题列表
- 避免重复相同的问题

### 3. 监控执行效果
- 定期查看统计信息
- 关注成功率变化
- 及时调整配置参数

### 4. 保持低调
- 使用随机间隔时间
- 避免固定的执行模式
- 不要设置过高的执行频率

## 安全和合规

### 数据安全
- 生成的问题都是通用编程问题
- 不包含项目特定信息
- 不会泄露敏感代码

### 使用合规
- 确保符合公司AI工具使用政策
- 遵守AI服务提供商的使用条款
- 不要滥用AI服务资源

### 隐私保护
- 插件不会收集个人信息
- 不会上传代码或项目数据
- 所有配置都保存在本地

## 常见使用场景

### 场景1：满足KPI要求
```
配置建议：
- 执行间隔: 60-120分钟
- 工作时间限制: 启用
- 仅工作日: 启用
- 问题类型: 全部启用
```

### 场景2：学习AI工具
```
配置建议：
- 执行间隔: 30-60分钟
- 时间限制: 关闭
- 自定义问题: 添加学习相关问题
- 手动触发: 经常使用
```

### 场景3：测试AI插件
```
配置建议：
- 使用手动触发功能
- 启用详细日志
- 测试不同的Action ID
- 监控成功率
```

通过合理配置和使用，这个插件可以帮助您自动化与AI编程助手的交互，既满足使用要求，又不会干扰正常的开发工作。
