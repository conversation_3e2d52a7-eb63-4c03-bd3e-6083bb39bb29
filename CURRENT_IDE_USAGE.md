# 在当前IDE中直接使用YTO AI KPI Helper

## 🎯 目标
在您当前的IntelliJ IDEA中直接调试和使用这个插件，而不是启动独立的IDE实例。

## 📦 构建插件

### 1. 构建插件包
```bash
./gradlew buildPlugin
```

构建完成后，插件文件将生成在：
```
build/distributions/YTOAIKpiHelper-1.0-SNAPSHOT.zip
```

### 2. 在当前IDE中安装插件

1. **打开插件设置**：
   - `File → Settings → Plugins` (Windows/Linux)
   - `IntelliJ IDEA → Preferences → Plugins` (macOS)

2. **安装插件**：
   - 点击齿轮图标 ⚙️ → `Install Plugin from Disk...`
   - 选择生成的 `YTOAIKpiHelper-1.0-SNAPSHOT.zip` 文件
   - 点击 `OK`

3. **重启IDE**：
   - 重启IntelliJ IDEA以激活插件

## 🔧 YtoAICode Tool Window配置

插件现在已经专门配置为查找Tool Window ID = "YtoAICode"：

### 自动检测方式
插件会按以下优先级尝试：

1. **Tool Window方式**（优先）：
   - 直接查找ID为"YtoAICode"的Tool Window
   - 如果找到，直接激活它

2. **Action ID方式**（备选）：
   - `ActivateYtoAICodeToolWindow`
   - `YtoAICode.ToolWindow`
   - `YtoAICode.Show`
   - `YtoAICode.Open`
   - `YtoAICode`

### 手动配置
如果自动检测失败，您可以：

1. **查看日志**：
   ```
   Help → Show Log in Finder/Explorer
   ```
   搜索关键词：`KpiHelper`

2. **手动添加Action ID**：
   - 使用UI Inspector找到正确的Action ID
   - 通过菜单添加：`Tools → AI助手自动化 → 配置设置`

## 🚀 使用方法

### 启动自动化
插件安装后会自动启动，您可以通过菜单控制：

**Tools → AI助手自动化**
- **启用/禁用自动化** - 开关功能
- **立即触发一次** - 测试功能
- **查看状态** - 查看运行状态
- **查找YTO AI插件** - 自动查找插件
- **配置设置** - 修改配置

### 验证是否工作
1. **查看启动日志**：
   ```
   KpiHelper: AI助手自动化插件已启动
   KpiHelper: 找到YtoAICode Tool Window
   ```

2. **手动测试**：
   - 点击 `立即触发一次`
   - 观察是否成功激活YtoAICode Tool Window

3. **查看统计**：
   - 点击 `查看状态`
   - 确认执行次数和成功率

## ⚙️ 默认配置

插件已预配置为专门支持YtoAICode：

```
Tool Window ID: YtoAICode
执行间隔: 60-180分钟（随机）
工作时间: 9:00-18:00
仅工作日: 否（默认）
优先使用Tool Window: 是
```

## 🔍 调试方法

### 1. 查看详细日志
```bash
# 在IDE中查看
Help → Show Log in Finder/Explorer

# 或者在终端查看
tail -f ~/Library/Logs/JetBrains/IntelliJIdea*/idea.log | grep KpiHelper
```

### 2. 手动查找Tool Window
在IDE中执行以下操作来确认YtoAICode Tool Window是否存在：
- `View → Tool Windows` - 查看是否有YtoAICode选项
- 或者查看IDE底部/侧边是否有YtoAICode标签

### 3. 使用UI Inspector
```
Help → Diagnostic Tools → UI Inspector
```
点击YtoAICode相关的UI元素来查看其Action ID。

## 🎛️ 高级配置

### 修改Tool Window ID
如果您的YTO AI插件使用不同的Tool Window ID，可以修改代码：

编辑文件：`src/main/kotlin/cn/leo/helper/PluginStartupListener.kt`
```kotlin
val toolWindow = toolWindowManager.getToolWindow("YtoAICode")
```
将 `"YtoAICode"` 替换为实际的Tool Window ID。

### 添加自定义Action ID
编辑文件：`src/main/kotlin/cn/leo/helper/KpiHelperConfig.kt`
```kotlin
var customAIActionIds: MutableList<String> = mutableListOf(
    "ActivateYtoAICodeToolWindow",
    "YtoAICode.ToolWindow",
    "YOUR_CUSTOM_ACTION_ID"  // 添加您的Action ID
)
```

## 🔄 重新构建和更新

如果修改了代码，需要重新构建和安装：

```bash
# 1. 重新构建
./gradlew clean buildPlugin

# 2. 在IDE中卸载旧版本
# Settings → Plugins → 找到YTOAIKpiHelper → Uninstall

# 3. 安装新版本
# Settings → Plugins → Install Plugin from Disk...

# 4. 重启IDE
```

## ✅ 成功标志

当一切配置正确时，您应该看到：

1. **IDE日志中显示**：
   ```
   KpiHelper: 找到YtoAICode Tool Window
   KpiHelper: 成功激活YtoAICode Tool Window
   ```

2. **YtoAICode Tool Window被自动激活**

3. **状态显示正常**：
   - 成功率 > 80%
   - 定期执行记录

## 🚨 常见问题

### Q: Tool Window没有被找到
**A**: 确认YTO AI插件已正确安装并启用，Tool Window ID确实是"YtoAICode"

### Q: 插件不执行
**A**: 检查插件是否启用：`Tools → AI助手自动化 → 启用/禁用自动化`

### Q: 想要修改执行频率
**A**: 修改配置文件中的 `minIntervalMinutes` 和 `maxIntervalMinutes` 参数

这样您就可以在当前IDE中直接使用和调试插件了！🎉
