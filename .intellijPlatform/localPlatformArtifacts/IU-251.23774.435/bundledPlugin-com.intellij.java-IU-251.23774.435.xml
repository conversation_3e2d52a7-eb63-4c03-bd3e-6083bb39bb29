<ivy-module version="2.0">
  <info organisation="bundledPlugin" module="com.intellij.java" revision="IU-251.23774.435"/>
  <configurations>
    <conf name="default" visibility="public"/>
  </configurations>
  <publications>
    <artifact name="jshell-protocol" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="jps-javac-extension" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="jps-launcher" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="javac2" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="maven-resolver-transport-http" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="jgoodies-common" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="java-impl" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="maven-resolver-transport-file" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="jps-builders-6" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="debugger-memory-agent" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="aether-dependency-resolver" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="jps-builders" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="jshell-frontend" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="maven-resolver-connector-basic" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="kotlin-metadata" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="java-frontback" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="sa-jdwp" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="jb-jdi" ext="jar" conf="default" url="plugins/java/lib"/>
    <artifact name="intellij.java.unscramble" ext="jar" conf="default" url="plugins/java/lib/modules"/>
    <artifact name="intellij.java.featuresTrainer" ext="jar" conf="default" url="plugins/java/lib/modules"/>
    <artifact name="intellij.java.debugger.impl.backend" ext="jar" conf="default" url="plugins/java/lib/modules"/>
    <artifact name="intellij.java.debugger.impl.frontend" ext="jar" conf="default" url="plugins/java/lib/modules"/>
    <artifact name="intellij.jvm.analysis.impl" ext="jar" conf="default" url="plugins/java/lib/modules"/>
    <artifact name="intellij.java.vcs" ext="jar" conf="default" url="plugins/java/lib/modules"/>
    <artifact name="intellij.java.structuralSearch" ext="jar" conf="default" url="plugins/java/lib/modules"/>
  </publications>
  <dependencies/>
</ivy-module>