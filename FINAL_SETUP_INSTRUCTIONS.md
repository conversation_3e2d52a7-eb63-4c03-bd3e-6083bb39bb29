# YTO AI KPI Helper - 最终设置说明

恭喜！您的YTO AI自动化插件已经成功构建。以下是完整的设置和使用说明。

## 🎉 构建成功

项目已经成功编译，现在可以开始使用了！

## 📋 快速开始步骤

### 1. 启动带有插件的IDE

```bash
# 在项目根目录执行
./gradlew runIde
```

这将启动一个新的IntelliJ IDEA实例，用于测试我们的自动化KPI助手插件。

**注意**：您需要确保YTO AI插件已经在您的主IDE中正确安装。

### 2. 查找YTO AI插件的Action ID

这是最关键的步骤！启动IDE后：

1. 打开任意项目
2. 点击菜单：**Tools → AI助手自动化 → 查找YTO AI插件**
3. 系统会自动扫描并尝试找到YTO AI插件的Action ID
4. 如果找到，选择"是"将其保存到配置中

### 3. 手动查找Action ID（如果自动查找失败）

如果自动查找失败，可以手动查找：

#### 方法A：使用UI Inspector
1. 启动UI Inspector：**Help → Diagnostic Tools → UI Inspector**
2. 点击YTO AI插件的按钮或菜单项
3. 在Inspector中查看Action ID
4. 记录下Action ID（例如：`yto.ai.chat`）

#### 方法B：查看日志
1. 点击菜单：**Tools → AI助手自动化 → 查看状态**
2. 查看输出中列出的所有相关Action
3. 找到YTO相关的Action ID

### 4. 配置Action ID

找到正确的Action ID后：

#### 通过界面配置：
1. 点击菜单：**Tools → AI助手自动化 → 配置设置**
2. 选择"查看详细配置"
3. 记下当前配置信息

#### 通过代码配置：
如果知道确切的Action ID，可以直接修改代码：

1. 编辑文件：`src/main/kotlin/cn/leo/helper/PluginStartupListener.kt`
2. 找到`tryPredefinedActions()`方法中的这一行：
   ```kotlin
   "YOUR_YTO_AI_PLUGIN_ACTION_ID_HERE"
   ```
3. 将其替换为实际的Action ID，例如：
   ```kotlin
   "yto.ai.chat"
   ```

### 5. 测试配置

配置完成后，测试是否工作：

1. 点击菜单：**Tools → AI助手自动化 → 立即触发一次**
2. 观察是否成功打开YTO AI插件
3. 查看执行结果对话框

## 🔧 功能说明

### 菜单功能
通过 **Tools → AI助手自动化** 可以访问：

- **启用/禁用自动化** - 快速开关自动化功能
- **立即触发一次** - 手动测试一次AI交互
- **配置设置** - 查看和修改配置
- **查看状态** - 查看运行状态和统计信息
- **查找YTO AI插件** - 自动查找YTO AI插件的Action ID

### 自动化特性

- **智能调度**: 每60-180分钟随机执行一次
- **工作时间限制**: 可设置仅在工作时间执行
- **问题生成**: 自动生成200+种编程相关问题
- **统计监控**: 记录执行次数和成功率
- **错误重试**: 自动重试失败的操作

## ⚙️ 推荐配置

### 基本设置
```
执行间隔: 90-150分钟（随机）
工作时间: 9:00-18:00
仅工作日: 是
最大重试: 3次
```

### 问题类型
建议启用以下问题类型：
- 编程通用
- 代码审查  
- 架构设计
- 调试排错

## 📊 监控和验证

### 查看运行状态
定期检查自动化是否正常工作：

1. 点击菜单：**Tools → AI助手自动化 → 查看状态**
2. 查看以下信息：
   - 检测到的YTO AI插件
   - 执行统计（总次数、成功率）
   - 下次执行时间

### 查看日志
在IDE日志中查找关键信息：
```
KpiHelper: AI助手自动化插件已启动
KpiHelper: 找到YTO AI插件: yto.ai.chat
KpiHelper: 已安排下次AI交互，将在 120 分钟后执行
KpiHelper: 成功触发AI插件动作: yto.ai.chat
```

## 🚨 故障排除

### 问题1：未找到YTO AI插件
**解决方案**：
1. 确认YTO AI插件在插件管理器中显示为已启用
2. 重启IDE
3. 使用UI Inspector手动查找Action ID
4. 确认YTO AI插件已通过正常渠道安装

### 问题2：自动化不执行
**解决方案**：
1. 检查插件是否启用：**Tools → AI助手自动化 → 启用/禁用自动化**
2. 确认当前时间在工作时间内
3. 查看详细日志获取错误信息

### 问题3：执行失败
**解决方案**：
1. 验证Action ID是否正确
2. 手动测试YTO AI插件是否正常工作
3. 尝试不同的Action ID

## 📝 常见YTO AI插件Action ID模式

根据常见命名规范，可能的Action ID包括：

```
# YTO特定模式
yto.ai.chat
yto.ai.assistant
yto.aicode.chat
yto.aicode.action
ytoai.action.chat

# 通用AI模式
ai.chat.action
ai.assistant.action
chat.action
ask.action
```

## 🔄 重新构建（如果需要修改代码）

如果需要修改代码后重新构建：

```bash
# 清理并重新构建
./gradlew clean build

# 运行新版本
./gradlew runIde
```

## ✅ 验证成功标志

当一切配置正确时，您应该看到：

1. **启动日志**：
   ```
   KpiHelper: AI助手自动化插件已启动，项目: YourProject
   KpiHelper: 找到YTO AI插件: [Action ID]
   ```

2. **状态显示**：
   - 检测到1个或多个YTO AI相关Action
   - 成功率 > 80%
   - 定期执行记录

3. **手动测试成功**：
   - "立即触发一次"能成功打开YTO AI插件
   - 显示执行成功的对话框

## 🎯 使用建议

1. **初期设置**：先使用较短的间隔（60-90分钟）进行测试
2. **稳定运行**：确认正常工作后，可以调整为更长间隔
3. **定期检查**：每周查看一次统计信息，确保正常运行
4. **适度使用**：避免过于频繁的执行，保持合理的使用频率

## 📞 技术支持

如果遇到问题：
1. 查看IDE日志获取详细错误信息
2. 使用"查看状态"功能检查当前配置
3. 确认YTO AI插件本身是否正常工作
4. 尝试手动触发功能进行调试

---

**恭喜您成功设置了YTO AI自动化助手！** 🎉

这个工具将帮助您轻松满足公司对AI工具使用的KPI要求，同时不会干扰您的正常开发工作。
